import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";
import { Id } from "../../convex/_generated/dataModel";

interface BusinessDashboardProps {
  onBack: () => void;
}

export function BusinessDashboard({ onBack }: BusinessDashboardProps) {
  const [selectedBusinessId, setSelectedBusinessId] = useState<Id<"businesses"> | null>(null);
  const [activeTab, setActiveTab] = useState<"overview" | "edit" | "media" | "reviews" | "settings">("overview");

  const myBusinesses = useQuery(api.businesses.getMyBusinesses);
  const selectedBusiness = selectedBusinessId 
    ? useQuery(api.businesses.getBySlug, { slug: myBusinesses?.find(b => b._id === selectedBusinessId)?.slug || "" })
    : null;
  const businessStats = selectedBusinessId 
    ? useQuery(api.businesses.getBusinessStats, { businessId: selectedBusinessId })
    : null;

  const updateBusinessStatus = useMutation(api.businesses.updateBusinessStatus);

  if (!myBusinesses) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  if (myBusinesses.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="max-w-md mx-auto">
          <div className="mb-4">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Businesses Found</h3>
          <p className="text-gray-600 mb-6">
            You haven't added any businesses yet. Start by adding your first business listing.
          </p>
          <button
            onClick={onBack}
            className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
          >
            Add Your First Business
          </button>
        </div>
      </div>
    );
  }

  const handleStatusToggle = async (businessId: Id<"businesses">, isActive: boolean) => {
    try {
      await updateBusinessStatus({
        businessId,
        isActive,
      });
      toast.success(`Business ${isActive ? 'activated' : 'deactivated'} successfully`);
    } catch (error) {
      toast.error("Failed to update business status");
    }
  };

  if (!selectedBusinessId) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Businesses</h1>
            <p className="text-gray-600">Manage your business listings</p>
          </div>
          <button
            onClick={onBack}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
          >
            Add New Business
          </button>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          {myBusinesses.map((business) => (
            <div key={business._id} className="bg-white rounded-lg shadow-md overflow-hidden">
              {business.logoUrl && (
                <img
                  src={business.logoUrl}
                  alt={business.name}
                  className="w-full h-48 object-cover"
                />
              )}
              <div className="p-6">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="text-lg font-semibold text-gray-900">{business.name}</h3>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      business.isActive 
                        ? "bg-green-100 text-green-800" 
                        : "bg-red-100 text-red-800"
                    }`}>
                      {business.isActive ? "Active" : "Inactive"}
                    </span>
                    {business.isVerified && (
                      <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                        Verified
                      </span>
                    )}
                  </div>
                </div>
                
                <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                  {business.description}
                </p>
                
                <div className="flex items-center text-sm text-gray-500 mb-3">
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  {business.city}, {business.state}
                </div>

                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <div className="flex text-yellow-400">
                      {[...Array(5)].map((_, i) => (
                        <svg
                          key={i}
                          className={`w-4 h-4 ${i < Math.floor(business.averageRating) ? 'fill-current' : 'text-gray-300'}`}
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      ))}
                    </div>
                    <span className="ml-2 text-sm text-gray-600">
                      ({business.reviewCount} reviews)
                    </span>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                  <button
                    onClick={() => setSelectedBusinessId(business._id)}
                    className="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm touch-manipulation"
                  >
                    Manage
                  </button>
                  <button
                    onClick={() => handleStatusToggle(business._id, !business.isActive)}
                    className={`px-4 py-2 rounded-lg text-sm transition-colors touch-manipulation ${
                      business.isActive
                        ? "bg-red-100 text-red-700 hover:bg-red-200"
                        : "bg-green-100 text-green-700 hover:bg-green-200"
                    }`}
                  >
                    {business.isActive ? "Deactivate" : "Activate"}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Individual business management view will be implemented in the next part
  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <button
          onClick={() => setSelectedBusinessId(null)}
          className="text-green-600 hover:text-green-700"
        >
          ← Back to My Businesses
        </button>
        <h1 className="text-2xl font-bold text-gray-900">
          {selectedBusiness?.name}
        </h1>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: "overview", label: "Overview" },
            { id: "edit", label: "Edit Info" },
            { id: "media", label: "Photos & Videos" },
            { id: "reviews", label: "Reviews" },
            { id: "settings", label: "Settings" },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? "border-green-500 text-green-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="bg-white rounded-lg shadow p-6">
        {activeTab === "overview" && (
          <div>
            <h3 className="text-lg font-medium mb-4">Business Overview</h3>
            {businessStats && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{businessStats.totalReviews}</div>
                  <div className="text-sm text-blue-600">Total Reviews</div>
                </div>
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">
                    {businessStats.averageRating.toFixed(1)}
                  </div>
                  <div className="text-sm text-yellow-600">Average Rating</div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {businessStats.mediaCount.images + businessStats.mediaCount.videos}
                  </div>
                  <div className="text-sm text-green-600">Media Files</div>
                </div>
              </div>
            )}
          </div>
        )}
        
        {activeTab === "edit" && (
          <div>
            <h3 className="text-lg font-medium mb-4">Edit Business Information</h3>
            <p className="text-gray-600">Business editing form will be implemented here.</p>
          </div>
        )}
        
        {activeTab === "media" && (
          <div>
            <h3 className="text-lg font-medium mb-4">Manage Photos & Videos</h3>
            <p className="text-gray-600">Media management interface will be implemented here.</p>
          </div>
        )}
        
        {activeTab === "reviews" && (
          <div>
            <h3 className="text-lg font-medium mb-4">Customer Reviews</h3>
            <p className="text-gray-600">Reviews management will be implemented here.</p>
          </div>
        )}
        
        {activeTab === "settings" && (
          <div>
            <h3 className="text-lg font-medium mb-4">Business Settings</h3>
            <p className="text-gray-600">Settings panel will be implemented here.</p>
          </div>
        )}
      </div>
    </div>
  );
}
