import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { YouTubePlayer } from "./YouTubePlayer";
import { useState } from "react";
import { toast } from "sonner";

interface BusinessDetailProps {
  businessSlug: string;
  onBack: () => void;
}

export function BusinessDetail({ businessSlug, onBack }: BusinessDetailProps) {
  const business = useQuery(api.businesses.getBySlug, { slug: businessSlug });
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [reviewData, setReviewData] = useState({
    rating: 5,
    title: "",
    comment: "",
  });

  const createReview = useMutation(api.reviews.create);

  if (!business) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  const handleSubmitReview = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await createReview({
        businessId: business._id,
        ...reviewData,
      });
      toast.success("Review submitted successfully!");
      setShowReviewForm(false);
      setReviewData({ rating: 5, title: "", comment: "" });
    } catch (error) {
      toast.error("Failed to submit review");
    }
  };

  const formatHours = (hours: any) => {
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    const dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    
    return days.map((day, index) => ({
      day: dayNames[index],
      hours: hours[day] || 'Closed'
    }));
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <button
          onClick={onBack}
          className="text-green-600 hover:text-green-700 mb-4 flex items-center"
        >
          ← Back to Directory
        </button>
      </div>

      {/* Business Info */}
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        <div className="p-8">
          <div className="flex flex-col lg:flex-row lg:items-start lg:space-x-8">
            {/* Logo and Basic Info */}
            <div className="flex-shrink-0 mb-6 lg:mb-0">
              {business.logoUrl && (
                <img
                  src={business.logoUrl}
                  alt={business.name}
                  className="w-32 h-32 rounded-lg object-cover mx-auto lg:mx-0"
                />
              )}
            </div>

            {/* Main Info */}
            <div className="flex-1">
              <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-6">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">
                    {business.name}
                  </h1>
                  <p className="text-lg text-gray-600 mb-4">
                    {business.description}
                  </p>
                  
                  {/* Categories */}
                  <div className="mb-4">
                    <div className="flex flex-wrap gap-2">
                      <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                        {business.category?.name}
                      </span>
                      {business.subcategories.filter((subcat): subcat is NonNullable<typeof subcat> => subcat !== null).map((subcat) => (
                        <span key={subcat._id} className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm">
                          {subcat.name}
                        </span>
                      ))}
                    </div>
                  </div>
                  
                  {/* Badges */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {business.isMuslimOwned && (
                      <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                        Muslim-Owned
                      </span>
                    )}
                    {business.isHalalCertified && (
                      <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                        Halal Certified
                      </span>
                    )}
                    {business.isVerified && (
                      <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">
                        Verified Listing
                      </span>
                    )}
                    {business.hasPrayerSpace && (
                      <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
                        Prayer Space Available
                      </span>
                    )}
                  </div>
                </div>

                {/* Rating */}
                {business.averageRating > 0 && (
                  <div className="text-center lg:text-right">
                    <div className="text-3xl font-bold text-gray-900">
                      {business.averageRating.toFixed(1)}
                    </div>
                    <div className="text-yellow-400 text-xl mb-1">
                      {'★'.repeat(Math.round(business.averageRating))}
                    </div>
                    <div className="text-sm text-gray-600">
                      {business.reviewCount} reviews
                    </div>
                  </div>
                )}
              </div>

              {/* Contact Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Contact Information</h3>
                  <div className="space-y-2 text-gray-600">
                    <div className="flex items-center">
                      <span className="w-5 h-5 mr-3">📍</span>
                      {business.address}, {business.city}, {business.state} {business.zipCode}
                    </div>
                    {business.phone && (
                      <div className="flex items-center">
                        <span className="w-5 h-5 mr-3">📞</span>
                        <a href={`tel:${business.phone}`} className="hover:text-green-600">
                          {business.phone}
                        </a>
                      </div>
                    )}
                    {business.email && (
                      <div className="flex items-center">
                        <span className="w-5 h-5 mr-3">✉️</span>
                        <a href={`mailto:${business.email}`} className="hover:text-green-600">
                          {business.email}
                        </a>
                      </div>
                    )}
                    {business.website && (
                      <div className="flex items-center">
                        <span className="w-5 h-5 mr-3">🌐</span>
                        <a 
                          href={business.website} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="hover:text-green-600"
                        >
                          Visit Website
                        </a>
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Business Hours</h3>
                  <div className="space-y-1 text-sm">
                    {formatHours(business.hours).map(({ day, hours }) => (
                      <div key={day} className="flex justify-between">
                        <span className="text-gray-600">{day}</span>
                        <span className="text-gray-900">{hours}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Media Gallery */}
      {(business.imageUrls.length > 0 || business.videoUrls.length > 0 || business.youtubeVideoUrl) && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">Gallery</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {business.imageUrls.map((image, index) => (
              <div key={image.id} className="aspect-video rounded-lg overflow-hidden">
                <img
                  src={image.url || ''}
                  alt={`${business.name} - Image ${index + 1}`}
                  className="w-full h-full object-cover"
                />
              </div>
            ))}
            {business.videoUrls.map((video, index) => (
              <div key={video.id} className="aspect-video rounded-lg overflow-hidden">
                <video
                  src={video.url || ''}
                  controls
                  className="w-full h-full object-cover"
                >
                  Your browser does not support the video tag.
                </video>
              </div>
            ))}

            {/* YouTube Video */}
            {business.youtubeVideoUrl && (
              <div className="md:col-span-2 lg:col-span-3">
                <YouTubePlayer
                  url={business.youtubeVideoUrl}
                  title={`${business.name} - Business Video`}
                  className="w-full"
                />
              </div>
            )}
          </div>
        </div>
      )}

      {/* Services */}
      {business.services.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">Services</h3>
          <div className="flex flex-wrap gap-2">
            {business.services.map((service, index) => (
              <span
                key={index}
                className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm"
              >
                {service}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Additional Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">Business Details</h3>
          <div className="space-y-3 text-sm">
            {business.yearsInBusiness && (
              <div className="flex justify-between">
                <span className="text-gray-600">Years in Business:</span>
                <span className="text-gray-900">{business.yearsInBusiness}</span>
              </div>
            )}
            <div className="flex justify-between">
              <span className="text-gray-600">Parking Available:</span>
              <span className="text-gray-900">{business.hasParking ? 'Yes' : 'No'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Wheelchair Accessible:</span>
              <span className="text-gray-900">{business.isAccessible ? 'Yes' : 'No'}</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">Payment & Languages</h3>
          <div className="space-y-4">
            <div>
              <span className="text-sm text-gray-600 block mb-2">Payment Methods:</span>
              <div className="flex flex-wrap gap-2">
                {business.paymentMethods.map((method, index) => (
                  <span
                    key={index}
                    className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs"
                  >
                    {method}
                  </span>
                ))}
              </div>
            </div>
            <div>
              <span className="text-sm text-gray-600 block mb-2">Languages Spoken:</span>
              <div className="flex flex-wrap gap-2">
                {business.languagesSpoken.map((language, index) => (
                  <span
                    key={index}
                    className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs"
                  >
                    {language}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Reviews Section */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-semibold text-gray-900">Customer Reviews</h3>
          <button
            onClick={() => setShowReviewForm(!showReviewForm)}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
          >
            Write a Review
          </button>
        </div>

        {/* Review Form */}
        {showReviewForm && (
          <form onSubmit={handleSubmitReview} className="mb-8 p-4 bg-gray-50 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Rating
                </label>
                <select
                  value={reviewData.rating}
                  onChange={(e) => setReviewData({ ...reviewData, rating: Number(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500"
                >
                  {[5, 4, 3, 2, 1].map(rating => (
                    <option key={rating} value={rating}>
                      {rating} Star{rating !== 1 ? 's' : ''}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Review Title
                </label>
                <input
                  type="text"
                  value={reviewData.title}
                  onChange={(e) => setReviewData({ ...reviewData, title: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500"
                  required
                />
              </div>
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Your Review
              </label>
              <textarea
                value={reviewData.comment}
                onChange={(e) => setReviewData({ ...reviewData, comment: e.target.value })}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500"
                required
              />
            </div>
            <div className="flex space-x-3">
              <button
                type="submit"
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
              >
                Submit Review
              </button>
              <button
                type="button"
                onClick={() => setShowReviewForm(false)}
                className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors"
              >
                Cancel
              </button>
            </div>
          </form>
        )}

        {/* Reviews List */}
        <div className="space-y-6">
          {business.reviews.map((review) => (
            <div key={review._id} className="border-b border-gray-200 pb-6 last:border-b-0">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h4 className="font-semibold text-gray-900">{review.title}</h4>
                  <div className="flex items-center mt-1">
                    <div className="text-yellow-400 mr-2">
                      {'★'.repeat(review.rating)}{'☆'.repeat(5 - review.rating)}
                    </div>
                    <span className="text-sm text-gray-600">
                      by {review.user?.name || 'Anonymous'}
                    </span>
                  </div>
                </div>
                <span className="text-sm text-gray-500">
                  {new Date(review._creationTime).toLocaleDateString()}
                </span>
              </div>
              <p className="text-gray-700">{review.comment}</p>
            </div>
          ))}
          
          {business.reviews.length === 0 && (
            <p className="text-gray-500 text-center py-8">
              No reviews yet. Be the first to review this business!
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
