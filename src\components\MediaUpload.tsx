import { useState, useRef, useCallback } from "react";
import { useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";
import { Id } from "../../convex/_generated/dataModel";

interface MediaUploadProps {
  businessId?: Id<"businesses">;
  onMediaUploaded?: (mediaId: Id<"_storage">, mediaType: "image" | "video") => void;
  maxFiles?: number;
  acceptedTypes?: string[];
  showPreview?: boolean;
  className?: string;
}

interface UploadedMedia {
  id: Id<"_storage">;
  type: "image" | "video";
  url: string;
  file: File;
}

export function MediaUpload({
  businessId,
  onMediaUploaded,
  maxFiles = 10,
  acceptedTypes = ["image/*", "video/*"],
  showPreview = true,
  className = "",
}: MediaUploadProps) {
  const [uploadedMedia, setUploadedMedia] = useState<UploadedMedia[]>([]);
  const [uploading, setUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const generateUploadUrl = useMutation(api.businesses.generateUploadUrl);
  const addMedia = useMutation(api.businesses.addMedia);

  const handleFiles = useCallback(async (files: FileList) => {
    if (files.length === 0) return;

    const fileArray = Array.from(files);
    
    // Validate file count
    if (uploadedMedia.length + fileArray.length > maxFiles) {
      toast.error(`Maximum ${maxFiles} files allowed`);
      return;
    }

    // Validate file types
    const invalidFiles = fileArray.filter(file => {
      return !acceptedTypes.some(type => {
        if (type === "image/*") return file.type.startsWith("image/");
        if (type === "video/*") return file.type.startsWith("video/");
        return file.type === type;
      });
    });

    if (invalidFiles.length > 0) {
      toast.error("Some files have invalid types");
      return;
    }

    // Validate file sizes (10MB for images, 100MB for videos)
    const oversizedFiles = fileArray.filter(file => {
      const maxSize = file.type.startsWith("image/") ? 10 * 1024 * 1024 : 100 * 1024 * 1024;
      return file.size > maxSize;
    });

    if (oversizedFiles.length > 0) {
      toast.error("Some files are too large (max 10MB for images, 100MB for videos)");
      return;
    }

    setUploading(true);

    try {
      const newMedia: UploadedMedia[] = [];

      for (const file of fileArray) {
        // Generate upload URL
        const uploadUrl = await generateUploadUrl();
        
        // Upload file
        const result = await fetch(uploadUrl, {
          method: "POST",
          headers: { "Content-Type": file.type },
          body: file,
        });

        if (!result.ok) {
          throw new Error(`Failed to upload ${file.name}`);
        }

        const { storageId } = await result.json();
        const mediaType = file.type.startsWith("image/") ? "image" : "video";

        // Add to business if businessId is provided
        if (businessId) {
          await addMedia({
            businessId,
            mediaId: storageId,
            mediaType,
          });
        }

        // Create preview URL
        const previewUrl = URL.createObjectURL(file);

        const mediaItem: UploadedMedia = {
          id: storageId,
          type: mediaType as "image" | "video",
          url: previewUrl,
          file,
        };

        newMedia.push(mediaItem);

        // Call callback if provided
        if (onMediaUploaded) {
          onMediaUploaded(storageId, mediaType as "image" | "video");
        }
      }

      setUploadedMedia(prev => [...prev, ...newMedia]);
      toast.success(`${fileArray.length} file(s) uploaded successfully`);
    } catch (error) {
      console.error("Upload error:", error);
      toast.error("Failed to upload files");
    } finally {
      setUploading(false);
    }
  }, [uploadedMedia.length, maxFiles, acceptedTypes, generateUploadUrl, addMedia, businessId, onMediaUploaded]);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFiles(e.dataTransfer.files);
    }
  }, [handleFiles]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFiles(e.target.files);
    }
  }, [handleFiles]);

  const removeMedia = useCallback((index: number) => {
    setUploadedMedia(prev => {
      const newMedia = [...prev];
      URL.revokeObjectURL(newMedia[index].url);
      newMedia.splice(index, 1);
      return newMedia;
    });
  }, []);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          dragActive
            ? "border-green-500 bg-green-50"
            : "border-gray-300 hover:border-gray-400"
        } ${uploading ? "opacity-50 pointer-events-none" : ""}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptedTypes.join(",")}
          onChange={handleInputChange}
          className="hidden"
        />
        
        <div className="space-y-2">
          <div className="text-gray-600">
            <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
              <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          </div>
          <div>
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              className="text-green-600 hover:text-green-700 font-medium"
              disabled={uploading}
            >
              {uploading ? "Uploading..." : "Click to upload"}
            </button>
            <span className="text-gray-500"> or drag and drop</span>
          </div>
          <p className="text-sm text-gray-500">
            Images (PNG, JPG, GIF up to 10MB) or Videos (MP4, MOV up to 100MB)
          </p>
          <p className="text-xs text-gray-400">
            {uploadedMedia.length}/{maxFiles} files uploaded
          </p>
        </div>
      </div>

      {/* Preview Grid */}
      {showPreview && uploadedMedia.length > 0 && (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3 sm:gap-4">
          {uploadedMedia.map((media, index) => (
            <div key={index} className="relative group">
              <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                {media.type === "image" ? (
                  <img
                    src={media.url}
                    alt={`Upload ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <video
                    src={media.url}
                    className="w-full h-full object-cover"
                    controls={false}
                    muted
                  />
                )}
              </div>
              <button
                type="button"
                onClick={() => removeMedia(index)}
                className="absolute top-1 right-1 sm:top-2 sm:right-2 bg-red-500 text-white rounded-full p-1 opacity-100 sm:opacity-0 group-hover:opacity-100 transition-opacity touch-manipulation"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
              <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                {media.type === "image" ? "IMG" : "VID"}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
