import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useEffect } from "react";

interface HomePageProps {
  onCategorySelect: (slug: string) => void;
  onAddBusiness: () => void;
}

export function HomePage({ onCategorySelect, onAddBusiness }: HomePageProps) {
  const categories = useQuery(api.categories.list);
  const featuredBusinesses = useQuery(api.businesses.getFeatured);
  const seedCategories = useMutation(api.categories.seedCategories);

  useEffect(() => {
    if (categories && categories.length === 0) {
      seedCategories();
    }
  }, [categories, seedCategories]);

  if (!categories || !featuredBusinesses) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-12">
      {/* Hero Section */}
      <div className="text-center py-12 bg-gradient-to-r from-green-600 to-blue-600 rounded-2xl text-white">
        <h1 className="text-4xl md:text-6xl font-bold mb-4">
          Muslim Business Directory
        </h1>
        <p className="text-xl md:text-2xl mb-8 opacity-90">
          Discover and support Muslim-owned businesses in your community
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={() => onCategorySelect("")}
            className="bg-white text-green-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
          >
            Browse Directory
          </button>
          <button
            onClick={onAddBusiness}
            className="bg-green-700 text-white px-8 py-3 rounded-lg font-semibold hover:bg-green-800 transition-colors"
          >
            Add Your Business
          </button>
        </div>
      </div>

      {/* Featured Businesses */}
      {featuredBusinesses.length > 0 && (
        <section>
          <h2 className="text-3xl font-bold text-gray-900 mb-8">Featured Businesses</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredBusinesses.map((business) => (
              <div
                key={business._id}
                className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6"
              >
                <div className="flex items-start space-x-4">
                  {business.logoUrl && (
                    <img
                      src={business.logoUrl}
                      alt={business.name}
                      className="w-16 h-16 rounded-lg object-cover"
                    />
                  )}
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      {business.name}
                    </h3>
                    <p className="text-gray-600 mb-3 line-clamp-2">
                      {business.description}
                    </p>
                    <div className="flex items-center space-x-4 text-sm">
                      <span className="text-gray-500">{business.city}</span>
                      {business.averageRating > 0 && (
                        <div className="flex items-center">
                          <span className="text-yellow-400">★</span>
                          <span className="ml-1 text-gray-600">
                            {business.averageRating.toFixed(1)} ({business.reviewCount})
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="flex flex-wrap gap-2 mt-3">
                      {business.isMuslimOwned && (
                        <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                          Muslim-Owned
                        </span>
                      )}
                      {business.isHalalCertified && (
                        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                          Halal Certified
                        </span>
                      )}
                      {business.isVerified && (
                        <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs font-medium">
                          Verified
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>
      )}

      {/* Categories */}
      <section>
        <h2 className="text-3xl font-bold text-gray-900 mb-8">Browse by Category</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {categories.map((category) => (
            <div
              key={category._id}
              className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 cursor-pointer"
              onClick={() => onCategorySelect(category.slug)}
            >
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                {category.name}
              </h3>
              <p className="text-gray-600 mb-4">{category.description}</p>
              <div className="space-y-2">
                {category.subcategories.slice(0, 4).map((sub) => (
                  <div
                    key={sub._id}
                    className="text-sm text-gray-500 hover:text-green-600 transition-colors"
                  >
                    • {sub.name}
                  </div>
                ))}
                {category.subcategories.length > 4 && (
                  <div className="text-sm text-gray-400">
                    +{category.subcategories.length - 4} more
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </section>
    </div>
  );
}
