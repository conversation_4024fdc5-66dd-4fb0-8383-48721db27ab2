import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

export const list = query({
  args: {},
  handler: async (ctx) => {
    const categories = await ctx.db.query("categories").order("asc").collect();
    
    // Organize into parent categories with their subcategories
    const parentCategories = categories.filter(cat => !cat.parentId);
    const subcategories = categories.filter(cat => cat.parentId);
    
    return parentCategories.map(parent => ({
      ...parent,
      subcategories: subcategories.filter(sub => sub.parentId === parent._id)
    }));
  },
});

export const getBySlug = query({
  args: { slug: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("categories")
      .withIndex("by_slug", (q) => q.eq("slug", args.slug))
      .unique();
  },
});

export const seedCategories = mutation({
  args: {},
  handler: async (ctx) => {
    // Check if categories already exist
    const existing = await ctx.db.query("categories").first();
    if (existing) return;

    // Create main categories
    const homeServices = await ctx.db.insert("categories", {
      name: "Home Services",
      slug: "home-services",
      description: "Professional services for your home",
      order: 1,
    });

    const construction = await ctx.db.insert("categories", {
      name: "Construction & Trades",
      slug: "construction-trades",
      description: "Building and trade professionals",
      order: 2,
    });

    const professional = await ctx.db.insert("categories", {
      name: "Professional Services",
      slug: "professional-services",
      description: "Business and professional services",
      order: 3,
    });

    const food = await ctx.db.insert("categories", {
      name: "Food & Hospitality",
      slug: "food-hospitality",
      description: "Restaurants, catering, and food services",
      order: 4,
    });

    const events = await ctx.db.insert("categories", {
      name: "Events & Wedding",
      slug: "events-wedding",
      description: "Event planning and wedding services",
      order: 5,
    });

    const retail = await ctx.db.insert("categories", {
      name: "Retail & Shopping",
      slug: "retail-shopping",
      description: "Shopping and retail businesses",
      order: 6,
    });

    const health = await ctx.db.insert("categories", {
      name: "Health & Wellness",
      slug: "health-wellness",
      description: "Healthcare and wellness services",
      order: 7,
    });

    const automotive = await ctx.db.insert("categories", {
      name: "Automotive",
      slug: "automotive",
      description: "Car services and automotive businesses",
      order: 8,
    });

    // Create subcategories
    const subcategories = [
      // Home Services
      { name: "Handyman", slug: "handyman", parentId: homeServices, order: 1 },
      { name: "Cleaning", slug: "cleaning", parentId: homeServices, order: 2 },
      { name: "Landscaping", slug: "landscaping", parentId: homeServices, order: 3 },
      { name: "Moving", slug: "moving", parentId: homeServices, order: 4 },
      
      // Construction & Trades
      { name: "General Contractors", slug: "general-contractors", parentId: construction, order: 1 },
      { name: "Plumbers", slug: "plumbers", parentId: construction, order: 2 },
      { name: "Electricians", slug: "electricians", parentId: construction, order: 3 },
      { name: "HVAC", slug: "hvac", parentId: construction, order: 4 },
      { name: "Roofing", slug: "roofing", parentId: construction, order: 5 },
      { name: "Painters", slug: "painters", parentId: construction, order: 6 },
      
      // Professional Services
      { name: "Accountants", slug: "accountants", parentId: professional, order: 1 },
      { name: "Lawyers", slug: "lawyers", parentId: professional, order: 2 },
      { name: "Real Estate Agents", slug: "real-estate", parentId: professional, order: 3 },
      { name: "Marketing", slug: "marketing", parentId: professional, order: 4 },
      { name: "IT Support", slug: "it-support", parentId: professional, order: 5 },
      
      // Food & Hospitality
      { name: "Restaurants", slug: "restaurants", parentId: food, order: 1 },
      { name: "Catering", slug: "catering", parentId: food, order: 2 },
      { name: "Bakers", slug: "bakers", parentId: food, order: 3 },
      { name: "Halal Grocers", slug: "halal-grocers", parentId: food, order: 4 },
      
      // Events & Wedding
      { name: "Event Planners", slug: "event-planners", parentId: events, order: 1 },
      { name: "Decorators", slug: "decorators", parentId: events, order: 2 },
      { name: "Photographers", slug: "photographers", parentId: events, order: 3 },
      { name: "Venues", slug: "venues", parentId: events, order: 4 },
      
      // Retail & Shopping
      { name: "Modest Clothing", slug: "modest-clothing", parentId: retail, order: 1 },
      { name: "Islamic Goods", slug: "islamic-goods", parentId: retail, order: 2 },
      { name: "Art", slug: "art", parentId: retail, order: 3 },
      { name: "Jewelry", slug: "jewelry", parentId: retail, order: 4 },
      
      // Health & Wellness
      { name: "Doctors", slug: "doctors", parentId: health, order: 1 },
      { name: "Dentists", slug: "dentists", parentId: health, order: 2 },
      { name: "Therapists", slug: "therapists", parentId: health, order: 3 },
      { name: "Pharmacies", slug: "pharmacies", parentId: health, order: 4 },
      
      // Automotive
      { name: "Mechanics", slug: "mechanics", parentId: automotive, order: 1 },
      { name: "Car Dealers", slug: "car-dealers", parentId: automotive, order: 2 },
      { name: "Detailing", slug: "detailing", parentId: automotive, order: 3 },
    ];

    for (const subcat of subcategories) {
      await ctx.db.insert("categories", subcat);
    }
  },
});
