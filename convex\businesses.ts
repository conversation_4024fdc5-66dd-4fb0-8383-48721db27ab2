import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

export const list = query({
  args: {
    categorySlug: v.optional(v.string()),
    city: v.optional(v.string()),
    search: v.optional(v.string()),
    featuredOnly: v.optional(v.boolean()),
    sortBy: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    let businesses;

    if (args.search && args.search.trim().length > 0) {
      // Search in both name and description
      const nameResults = await ctx.db
        .query("businesses")
        .withSearchIndex("search_businesses", (q) => {
          let query = q.search("name", args.search!);
          if (args.city) {
            query = query.eq("city", args.city);
          }
          return query.eq("isActive", true);
        })
        .take(25);

      const descriptionResults = await ctx.db
        .query("businesses")
        .withSearchIndex("search_businesses_full", (q) => {
          let query = q.search("description", args.search!);
          if (args.city) {
            query = query.eq("city", args.city);
          }
          return query.eq("isActive", true);
        })
        .take(25);

      // Combine and deduplicate results
      const allResults = [...nameResults, ...descriptionResults];
      const uniqueBusinesses = allResults.filter((business, index, self) =>
        index === self.findIndex(b => b._id === business._id)
      );

      businesses = uniqueBusinesses;
    } else {
      // Regular query
      businesses = await ctx.db
        .query("businesses")
        .filter((q) => q.eq(q.field("isActive"), true))
        .order("desc")
        .take(50);
    }

    // Filter by category if specified
    if (args.categorySlug) {
      const category = await ctx.db
        .query("categories")
        .withIndex("by_slug", (q) => q.eq("slug", args.categorySlug!))
        .unique();

      if (category) {
        // If it's a parent category, include businesses with this categoryId or any of its subcategories
        if (!category.parentId) {
          // Get all subcategories of this parent category
          const subcategories = await ctx.db
            .query("categories")
            .withIndex("by_parent", (q) => q.eq("parentId", category._id))
            .collect();

          const subcategoryIds = subcategories.map(sub => sub._id);

          businesses = businesses.filter(b =>
            b.categoryId === category._id ||
            subcategoryIds.some(subId => b.subcategoryIds.includes(subId))
          );
        } else {
          // If it's a subcategory, check if it's in the business's subcategoryIds
          businesses = businesses.filter(b =>
            b.subcategoryIds.includes(category._id)
          );
        }
      }
    }

    // Filter by city if specified
    if (args.city && !args.search) {
      businesses = businesses.filter(b =>
        b.city.toLowerCase() === args.city!.toLowerCase()
      );
    }

    // Filter featured only if specified
    if (args.featuredOnly) {
      businesses = businesses.filter(b => b.isFeatured);
    }

    // Get additional data for each business
    const businessesWithData = await Promise.all(
      businesses.map(async (business) => {
        const category = await ctx.db.get(business.categoryId);
        const logoUrl = business.logoId ? await ctx.storage.getUrl(business.logoId) : null;
        
        // Get subcategories
        const subcategories = await Promise.all(
          business.subcategoryIds.map(id => ctx.db.get(id))
        );
        
        // Get average rating
        const reviews = await ctx.db
          .query("reviews")
          .withIndex("by_business", (q) => q.eq("businessId", business._id))
          .collect();
        
        const averageRating = reviews.length > 0 
          ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
          : 0;

        return {
          ...business,
          category,
          subcategories: subcategories.filter(Boolean),
          logoUrl,
          averageRating,
          reviewCount: reviews.length,
        };
      })
    );

    // Sort results if specified
    if (args.sortBy) {
      businessesWithData.sort((a: any, b: any) => {
        switch (args.sortBy) {
          case "name":
            return a.name.localeCompare(b.name);
          case "rating":
            return b.averageRating - a.averageRating;
          case "reviews":
            return b.reviewCount - a.reviewCount;
          case "newest":
            return b._creationTime - a._creationTime;
          default:
            return 0;
        }
      });
    }

    return businessesWithData;
  },
});

export const getBySlug = query({
  args: { slug: v.string() },
  handler: async (ctx, args) => {
    const business = await ctx.db
      .query("businesses")
      .withIndex("by_slug", (q) => q.eq("slug", args.slug))
      .unique();

    if (!business) return null;

    const category = await ctx.db.get(business.categoryId);
    const logoUrl = business.logoId ? await ctx.storage.getUrl(business.logoId) : null;
    
    // Get subcategories
    const subcategories = await Promise.all(
      business.subcategoryIds.map(id => ctx.db.get(id))
    );
    
    // Get business images
    const imageUrls = await Promise.all(
      business.imageIds.map(async (imageId) => {
        const url = await ctx.storage.getUrl(imageId);
        return { id: imageId, url };
      })
    );

    // Get business videos
    const videoUrls = await Promise.all(
      (business.videoIds || []).map(async (videoId) => {
        const url = await ctx.storage.getUrl(videoId);
        return { id: videoId, url };
      })
    );

    // Get reviews with user data
    const reviewsData = await ctx.db
      .query("reviews")
      .withIndex("by_business", (q) => q.eq("businessId", business._id))
      .collect();

    const reviews = await Promise.all(
      reviewsData.map(async (review) => {
        const user = await ctx.db.get(review.userId);
        return {
          ...review,
          user: user ? { name: user.name, email: user.email } : null,
        };
      })
    );

    const averageRating = reviews.length > 0 
      ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
      : 0;

    return {
      ...business,
      category,
      subcategories: subcategories.filter(Boolean),
      logoUrl,
      imageUrls,
      videoUrls,
      reviews,
      averageRating,
      reviewCount: reviews.length,
    };
  },
});

export const getFeatured = query({
  args: {},
  handler: async (ctx) => {
    const businesses = await ctx.db
      .query("businesses")
      .withIndex("by_featured", (q) => q.eq("isFeatured", true))
      .filter((q) => q.eq(q.field("isActive"), true))
      .take(6);

    return await Promise.all(
      businesses.map(async (business) => {
        const category = await ctx.db.get(business.categoryId);
        const logoUrl = business.logoId ? await ctx.storage.getUrl(business.logoId) : null;
        
        const reviews = await ctx.db
          .query("reviews")
          .withIndex("by_business", (q) => q.eq("businessId", business._id))
          .collect();
        
        const averageRating = reviews.length > 0 
          ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
          : 0;

        return {
          ...business,
          category,
          logoUrl,
          averageRating,
          reviewCount: reviews.length,
        };
      })
    );
  },
});

export const create = mutation({
  args: {
    name: v.string(),
    description: v.string(),
    categoryId: v.id("categories"),
    subcategoryIds: v.array(v.id("categories")),
    address: v.string(),
    city: v.string(),
    state: v.string(),
    zipCode: v.string(),
    phone: v.optional(v.string()),
    email: v.optional(v.string()),
    website: v.optional(v.string()),
    services: v.array(v.string()),
    paymentMethods: v.array(v.string()),
    languagesSpoken: v.array(v.string()),
    hours: v.object({
      monday: v.optional(v.string()),
      tuesday: v.optional(v.string()),
      wednesday: v.optional(v.string()),
      thursday: v.optional(v.string()),
      friday: v.optional(v.string()),
      saturday: v.optional(v.string()),
      sunday: v.optional(v.string()),
    }),
    isMuslimOwned: v.boolean(),
    isHalalCertified: v.boolean(),
    hasPrayerSpace: v.boolean(),
    hasParking: v.boolean(),
    isAccessible: v.boolean(),
    yearsInBusiness: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    
    // Generate slug from name
    const slug = args.name.toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');

    const businessId = await ctx.db.insert("businesses", {
      ...args,
      slug,
      imageIds: [],
      videoIds: [],
      serviceArea: [args.city],
      licenses: [],
      isVerified: false,
      ownerId: userId || undefined,
      isFeatured: false,
      isActive: true,
    });

    return businessId;
  },
});

export const generateUploadUrl = mutation({
  args: {},
  handler: async (ctx) => {
    return await ctx.storage.generateUploadUrl();
  },
});

export const addMedia = mutation({
  args: {
    businessId: v.id("businesses"),
    mediaId: v.id("_storage"),
    mediaType: v.string(), // "image" or "video"
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Must be logged in to add media");
    }

    const business = await ctx.db.get(args.businessId);
    if (!business) {
      throw new Error("Business not found");
    }

    if (business.ownerId !== userId) {
      throw new Error("Only business owner can add media");
    }

    if (args.mediaType === "image") {
      await ctx.db.patch(args.businessId, {
        imageIds: [...business.imageIds, args.mediaId],
      });
    } else if (args.mediaType === "video") {
      await ctx.db.patch(args.businessId, {
        videoIds: [...(business.videoIds || []), args.mediaId],
      });
    }

    return { success: true };
  },
});

export const removeMedia = mutation({
  args: {
    businessId: v.id("businesses"),
    mediaId: v.id("_storage"),
    mediaType: v.string(), // "image" or "video"
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Must be logged in to remove media");
    }

    const business = await ctx.db.get(args.businessId);
    if (!business) {
      throw new Error("Business not found");
    }

    if (business.ownerId !== userId) {
      throw new Error("Only business owner can remove media");
    }

    if (args.mediaType === "image") {
      await ctx.db.patch(args.businessId, {
        imageIds: business.imageIds.filter(id => id !== args.mediaId),
      });
    } else if (args.mediaType === "video") {
      await ctx.db.patch(args.businessId, {
        videoIds: (business.videoIds || []).filter(id => id !== args.mediaId),
      });
    }

    // Delete the file from storage
    await ctx.storage.delete(args.mediaId);

    return { success: true };
  },
});

export const getMyBusinesses = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    const businesses = await ctx.db
      .query("businesses")
      .withIndex("by_owner", (q) => q.eq("ownerId", userId))
      .collect();

    return await Promise.all(
      businesses.map(async (business) => {
        const category = await ctx.db.get(business.categoryId);
        const logoUrl = business.logoId ? await ctx.storage.getUrl(business.logoId) : null;

        // Get subcategories
        const subcategories = await Promise.all(
          business.subcategoryIds.map(id => ctx.db.get(id))
        );

        // Get average rating
        const reviews = await ctx.db
          .query("reviews")
          .withIndex("by_business", (q) => q.eq("businessId", business._id))
          .collect();

        const averageRating = reviews.length > 0
          ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
          : 0;

        return {
          ...business,
          category,
          subcategories: subcategories.filter(Boolean),
          logoUrl,
          averageRating,
          reviewCount: reviews.length,
        };
      })
    );


  },
});

export const searchSuggestions = query({
  args: {
    query: v.string(),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    if (!args.query || args.query.trim().length < 2) {
      return [];
    }

    const limit = args.limit || 5;

    // Search for business names
    const nameMatches = await ctx.db
      .query("businesses")
      .withSearchIndex("search_businesses", (q) =>
        q.search("name", args.query).eq("isActive", true)
      )
      .take(limit);

    // Get unique suggestions
    const suggestions = nameMatches.map(business => ({
      type: "business" as const,
      text: business.name,
      slug: business.slug,
      category: business.categoryId,
    }));

    // Add category suggestions
    const categories = await ctx.db.query("categories").collect();
    const categoryMatches = categories
      .filter(cat =>
        cat.name.toLowerCase().includes(args.query.toLowerCase())
      )
      .slice(0, Math.max(0, limit - suggestions.length))
      .map(cat => ({
        type: "category" as const,
        text: cat.name,
        slug: cat.slug,
        category: cat._id,
      }));

    return [...suggestions, ...categoryMatches];
  },
});

export const updateBusiness = mutation({
  args: {
    businessId: v.id("businesses"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    categoryId: v.optional(v.id("categories")),
    subcategoryIds: v.optional(v.array(v.id("categories"))),
    address: v.optional(v.string()),
    city: v.optional(v.string()),
    state: v.optional(v.string()),
    zipCode: v.optional(v.string()),
    phone: v.optional(v.string()),
    email: v.optional(v.string()),
    website: v.optional(v.string()),
    services: v.optional(v.array(v.string())),
    paymentMethods: v.optional(v.array(v.string())),
    languagesSpoken: v.optional(v.array(v.string())),
    hours: v.optional(v.object({
      monday: v.optional(v.string()),
      tuesday: v.optional(v.string()),
      wednesday: v.optional(v.string()),
      thursday: v.optional(v.string()),
      friday: v.optional(v.string()),
      saturday: v.optional(v.string()),
      sunday: v.optional(v.string()),
    })),
    isMuslimOwned: v.optional(v.boolean()),
    isHalalCertified: v.optional(v.boolean()),
    hasPrayerSpace: v.optional(v.boolean()),
    hasParking: v.optional(v.boolean()),
    isAccessible: v.optional(v.boolean()),
    yearsInBusiness: v.optional(v.number()),
    facebookUrl: v.optional(v.string()),
    instagramUrl: v.optional(v.string()),
    twitterUrl: v.optional(v.string()),
    linkedinUrl: v.optional(v.string()),
    youtubeVideoUrl: v.optional(v.string()),
    currentOffer: v.optional(v.string()),
    offerExpiry: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Must be logged in to update business");
    }

    const business = await ctx.db.get(args.businessId);
    if (!business) {
      throw new Error("Business not found");
    }

    if (business.ownerId !== userId) {
      throw new Error("Only business owner can update business");
    }

    const { businessId, ...updateData } = args;

    // Filter out undefined values
    const filteredUpdateData = Object.fromEntries(
      Object.entries(updateData).filter(([_, value]) => value !== undefined)
    );

    // Update slug if name is being changed
    if (filteredUpdateData.name && typeof filteredUpdateData.name === 'string') {
      filteredUpdateData.slug = filteredUpdateData.name.toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
    }

    await ctx.db.patch(businessId, filteredUpdateData);

    return { success: true };
  },
});

export const updateBusinessStatus = mutation({
  args: {
    businessId: v.id("businesses"),
    isActive: v.boolean(),
    temporaryMessage: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Must be logged in to update business status");
    }

    const business = await ctx.db.get(args.businessId);
    if (!business) {
      throw new Error("Business not found");
    }

    if (business.ownerId !== userId) {
      throw new Error("Only business owner can update business status");
    }

    await ctx.db.patch(args.businessId, {
      isActive: args.isActive,
      currentOffer: args.temporaryMessage || business.currentOffer,
    });

    return { success: true };
  },
});

export const seedBusinesses = mutation({
  args: {},
  handler: async (ctx) => {
    // Check if businesses already exist
    const existing = await ctx.db.query("businesses").first();
    if (existing) return;

    // Get categories for assignment
    const categories = await ctx.db.query("categories").collect();
    const constructionCategory = categories.find(c => c.slug === "construction-trades");
    const generalContractorsCategory = categories.find(c => c.slug === "general-contractors");
    const homeServicesCategory = categories.find(c => c.slug === "home-services");
    const handymanCategory = categories.find(c => c.slug === "handyman");
    const foodCategory = categories.find(c => c.slug === "food-hospitality");
    const restaurantsCategory = categories.find(c => c.slug === "restaurants");

    if (!constructionCategory || !generalContractorsCategory || !homeServicesCategory || !handymanCategory || !foodCategory || !restaurantsCategory) {
      throw new Error("Required categories not found. Please seed categories first.");
    }

    // Create sample businesses
    const sampleBusinesses = [
      {
        name: "GTA Fencing",
        slug: "gta-fencing",
        description: "Professional fencing installation and repair services for residential and commercial properties. We specialize in wood, vinyl, chain link, and aluminum fencing.",
        services: ["Fence Installation", "Fence Repair", "Gate Installation", "Custom Fencing"],
        categoryId: constructionCategory._id,
        subcategoryIds: [generalContractorsCategory._id],
        address: "123 Main Street",
        city: "Toronto",
        state: "ON",
        zipCode: "M1A 1A1",
        phone: "(*************",
        email: "<EMAIL>",
        website: "https://gtafencing.com",
        hours: {
          monday: "8:00 AM - 6:00 PM",
          tuesday: "8:00 AM - 6:00 PM",
          wednesday: "8:00 AM - 6:00 PM",
          thursday: "8:00 AM - 6:00 PM",
          friday: "8:00 AM - 6:00 PM",
          saturday: "9:00 AM - 4:00 PM",
          sunday: "Closed",
        },
        isMuslimOwned: true,
        isHalalCertified: false,
        isVerified: true,
        hasPrayerSpace: false,
        hasParking: true,
        isAccessible: true,
        yearsInBusiness: 8,
        paymentMethods: ["Cash", "Credit Card", "Debit Card", "Check"],
        languagesSpoken: ["English", "Arabic"],
        serviceArea: ["Toronto", "Mississauga", "Brampton"],
        licenses: ["Ontario Contractor License"],
        imageIds: [],
        videoIds: [],
        isFeatured: true,
        isActive: true,
      },
      {
        name: "Ahmed's Home Repairs",
        slug: "ahmeds-home-repairs",
        description: "Reliable handyman services for all your home repair needs. From small fixes to major renovations, we handle it all with quality craftsmanship.",
        services: ["Home Repairs", "Painting", "Plumbing", "Electrical Work", "Carpentry"],
        categoryId: homeServicesCategory._id,
        subcategoryIds: [handymanCategory._id],
        address: "456 Oak Avenue",
        city: "Mississauga",
        state: "ON",
        zipCode: "L5A 2B2",
        phone: "(*************",
        email: "<EMAIL>",
        website: "https://ahmedshomerepairs.com",
        hours: {
          monday: "7:00 AM - 7:00 PM",
          tuesday: "7:00 AM - 7:00 PM",
          wednesday: "7:00 AM - 7:00 PM",
          thursday: "7:00 AM - 7:00 PM",
          friday: "7:00 AM - 7:00 PM",
          saturday: "8:00 AM - 5:00 PM",
          sunday: "Closed",
        },
        isMuslimOwned: true,
        isHalalCertified: false,
        isVerified: true,
        hasPrayerSpace: false,
        hasParking: true,
        isAccessible: true,
        yearsInBusiness: 12,
        paymentMethods: ["Cash", "Credit Card", "E-transfer"],
        languagesSpoken: ["English", "Urdu", "Punjabi"],
        serviceArea: ["Mississauga", "Toronto", "Oakville"],
        licenses: ["Home Improvement License"],
        imageIds: [],
        videoIds: [],
        isFeatured: false,
        isActive: true,
      },
      {
        name: "Halal Delights Restaurant",
        slug: "halal-delights-restaurant",
        description: "Authentic Middle Eastern and Pakistani cuisine prepared with the finest halal ingredients. Family-owned restaurant serving the community for over 15 years.",
        services: ["Dine-in", "Takeout", "Delivery", "Catering", "Private Events"],
        categoryId: foodCategory._id,
        subcategoryIds: [restaurantsCategory._id],
        address: "789 Food Court Plaza",
        city: "Brampton",
        state: "ON",
        zipCode: "L6T 3C3",
        phone: "(*************",
        email: "<EMAIL>",
        website: "https://halaldelights.com",
        hours: {
          monday: "11:00 AM - 10:00 PM",
          tuesday: "11:00 AM - 10:00 PM",
          wednesday: "11:00 AM - 10:00 PM",
          thursday: "11:00 AM - 10:00 PM",
          friday: "11:00 AM - 11:00 PM",
          saturday: "11:00 AM - 11:00 PM",
          sunday: "12:00 PM - 9:00 PM",
        },
        isMuslimOwned: true,
        isHalalCertified: true,
        isVerified: true,
        hasPrayerSpace: true,
        hasParking: true,
        isAccessible: true,
        yearsInBusiness: 15,
        paymentMethods: ["Cash", "Credit Card", "Debit Card"],
        languagesSpoken: ["English", "Arabic", "Urdu"],
        serviceArea: ["Brampton", "Mississauga", "Toronto"],
        licenses: ["Food Service License", "Liquor License"],
        imageIds: [],
        videoIds: [],
        isFeatured: true,
        isActive: true,
      }
    ];

    // Insert sample businesses
    for (const business of sampleBusinesses) {
      await ctx.db.insert("businesses", business);
    }

    return { success: true, count: sampleBusinesses.length };
  },
});

export const reorderMedia = mutation({
  args: {
    businessId: v.id("businesses"),
    mediaIds: v.array(v.id("_storage")),
    mediaType: v.string(), // "image" or "video"
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Must be logged in to reorder media");
    }

    const business = await ctx.db.get(args.businessId);
    if (!business) {
      throw new Error("Business not found");
    }

    if (business.ownerId !== userId) {
      throw new Error("Only business owner can reorder media");
    }

    if (args.mediaType === "image") {
      await ctx.db.patch(args.businessId, {
        imageIds: args.mediaIds,
      });
    } else if (args.mediaType === "video") {
      await ctx.db.patch(args.businessId, {
        videoIds: args.mediaIds,
      });
    }

    return { success: true };
  },
});

export const setBusinessLogo = mutation({
  args: {
    businessId: v.id("businesses"),
    logoId: v.optional(v.id("_storage")),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Must be logged in to set business logo");
    }

    const business = await ctx.db.get(args.businessId);
    if (!business) {
      throw new Error("Business not found");
    }

    if (business.ownerId !== userId) {
      throw new Error("Only business owner can set business logo");
    }

    await ctx.db.patch(args.businessId, {
      logoId: args.logoId,
    });

    return { success: true };
  },
});

export const getBusinessStats = query({
  args: {
    businessId: v.id("businesses"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Must be logged in to view business stats");
    }

    const business = await ctx.db.get(args.businessId);
    if (!business) {
      throw new Error("Business not found");
    }

    if (business.ownerId !== userId) {
      throw new Error("Only business owner can view business stats");
    }

    // Get reviews
    const reviews = await ctx.db
      .query("reviews")
      .withIndex("by_business", (q) => q.eq("businessId", args.businessId))
      .collect();

    const averageRating = reviews.length > 0
      ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
      : 0;

    // Calculate rating distribution
    const ratingDistribution = [1, 2, 3, 4, 5].map(rating => ({
      rating,
      count: reviews.filter(r => r.rating === rating).length
    }));

    // Get recent reviews (last 30 days)
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
    const recentReviews = reviews.filter(r => r._creationTime > thirtyDaysAgo);

    return {
      totalReviews: reviews.length,
      averageRating,
      ratingDistribution,
      recentReviewsCount: recentReviews.length,
      mediaCount: {
        images: business.imageIds.length,
        videos: (business.videoIds || []).length,
      },
      isActive: business.isActive,
      isVerified: business.isVerified,
      isFeatured: business.isFeatured,
    };
  },
});
