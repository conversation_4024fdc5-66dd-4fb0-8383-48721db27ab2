import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { authTables } from "@convex-dev/auth/server";

const applicationTables = {
  categories: defineTable({
    name: v.string(),
    slug: v.string(),
    description: v.optional(v.string()),
    parentId: v.optional(v.id("categories")),
    order: v.number(),
  }).index("by_slug", ["slug"])
    .index("by_parent", ["parentId"]),

  businesses: defineTable({
    // Core Information
    name: v.string(),
    slug: v.string(),
    description: v.string(),
    services: v.array(v.string()),
    
    // Contact Information
    phone: v.optional(v.string()),
    email: v.optional(v.string()),
    website: v.optional(v.string()),
    
    // Address & Location
    address: v.string(),
    city: v.string(),
    state: v.string(),
    zipCode: v.string(),
    latitude: v.optional(v.number()),
    longitude: v.optional(v.number()),
    
    // Business Hours
    hours: v.object({
      monday: v.optional(v.string()),
      tuesday: v.optional(v.string()),
      wednesday: v.optional(v.string()),
      thursday: v.optional(v.string()),
      friday: v.optional(v.string()),
      saturday: v.optional(v.string()),
      sunday: v.optional(v.string()),
    }),
    
    // Categories - Updated to support multiple subcategories
    categoryId: v.id("categories"),
    subcategoryIds: v.array(v.id("categories")),
    
    // Trust & Credibility
    isMuslimOwned: v.boolean(),
    isHalalCertified: v.boolean(),
    isVerified: v.boolean(),
    yearsInBusiness: v.optional(v.number()),
    licenses: v.array(v.string()),
    
    // Media - Updated to support videos
    logoId: v.optional(v.id("_storage")),
    imageIds: v.array(v.id("_storage")),
    videoIds: v.optional(v.array(v.id("_storage"))),
    
    // Business Details - Enhanced payment methods and languages
    serviceArea: v.array(v.string()),
    paymentMethods: v.array(v.string()),
    languagesSpoken: v.array(v.string()),
    hasPrayerSpace: v.boolean(),
    hasParking: v.boolean(),
    isAccessible: v.boolean(),
    
    // Social Media
    facebookUrl: v.optional(v.string()),
    instagramUrl: v.optional(v.string()),
    twitterUrl: v.optional(v.string()),
    linkedinUrl: v.optional(v.string()),
    youtubeVideoUrl: v.optional(v.string()),
    
    // Listing Management
    ownerId: v.optional(v.id("users")),
    isFeatured: v.boolean(),
    isActive: v.boolean(),
    
    // Special Offers
    currentOffer: v.optional(v.string()),
    offerExpiry: v.optional(v.number()),
  }).index("by_slug", ["slug"])
    .index("by_category", ["categoryId"])
    .index("by_owner", ["ownerId"])
    .index("by_featured", ["isFeatured"])
    .index("by_city", ["city"])
    .searchIndex("search_businesses", {
      searchField: "name",
      filterFields: ["categoryId", "city", "isMuslimOwned", "isHalalCertified", "isActive"]
    })
    .searchIndex("search_businesses_full", {
      searchField: "description",
      filterFields: ["categoryId", "city", "isMuslimOwned", "isHalalCertified", "isActive"]
    }),

  reviews: defineTable({
    businessId: v.id("businesses"),
    userId: v.id("users"),
    rating: v.number(), // 1-5
    title: v.string(),
    comment: v.string(),
    isVerified: v.boolean(),
  }).index("by_business", ["businessId"])
    .index("by_user", ["userId"]),

  businessClaims: defineTable({
    businessId: v.id("businesses"),
    userId: v.id("users"),
    status: v.string(), // "pending", "approved", "rejected"
    message: v.optional(v.string()),
  }).index("by_business", ["businessId"])
    .index("by_user", ["userId"])
    .index("by_status", ["status"]),
};

export default defineSchema({
  ...authTables,
  ...applicationTables,
});
