import { useState } from "react";

interface BusinessHours {
  monday?: string;
  tuesday?: string;
  wednesday?: string;
  thursday?: string;
  friday?: string;
  saturday?: string;
  sunday?: string;
}

interface BusinessHoursManagerProps {
  hours: BusinessHours;
  onHoursChange: (hours: BusinessHours) => void;
  showSpecialHours?: boolean;
  className?: string;
}

const DAYS = [
  { key: "monday", label: "Monday" },
  { key: "tuesday", label: "Tuesday" },
  { key: "wednesday", label: "Wednesday" },
  { key: "thursday", label: "Thursday" },
  { key: "friday", label: "Friday" },
  { key: "saturday", label: "Saturday" },
  { key: "sunday", label: "Sunday" },
];

const PRESET_HOURS = [
  { label: "Closed", value: "Closed" },
  { label: "6:00 AM - 6:00 PM", value: "6:00 AM - 6:00 PM" },
  { label: "7:00 AM - 7:00 PM", value: "7:00 AM - 7:00 PM" },
  { label: "8:00 AM - 8:00 PM", value: "8:00 AM - 8:00 PM" },
  { label: "9:00 AM - 5:00 PM", value: "9:00 AM - 5:00 PM" },
  { label: "9:00 AM - 6:00 PM", value: "9:00 AM - 6:00 PM" },
  { label: "9:00 AM - 9:00 PM", value: "9:00 AM - 9:00 PM" },
  { label: "10:00 AM - 6:00 PM", value: "10:00 AM - 6:00 PM" },
  { label: "10:00 AM - 8:00 PM", value: "10:00 AM - 8:00 PM" },
  { label: "24 Hours", value: "24 Hours" },
  { label: "Custom", value: "Custom" },
];

const TIME_OPTIONS = [];
for (let hour = 0; hour < 24; hour++) {
  for (let minute = 0; minute < 60; minute += 30) {
    const time12 = new Date(2000, 0, 1, hour, minute).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
    TIME_OPTIONS.push(time12);
  }
}

export function BusinessHoursManager({
  hours,
  onHoursChange,
  showSpecialHours = false,
  className = "",
}: BusinessHoursManagerProps) {
  const [customHours, setCustomHours] = useState<Record<string, { open: string; close: string }>>({});
  const [bulkEditMode, setBulkEditMode] = useState(false);
  const [bulkHours, setBulkHours] = useState("9:00 AM - 5:00 PM");

  const handleHourChange = (day: string, value: string) => {
    if (value === "Custom") {
      // Initialize custom hours if not set
      if (!customHours[day]) {
        setCustomHours(prev => ({
          ...prev,
          [day]: { open: "9:00 AM", close: "5:00 PM" }
        }));
      }
    } else {
      // Remove custom hours if switching away from custom
      if (customHours[day]) {
        setCustomHours(prev => {
          const newCustom = { ...prev };
          delete newCustom[day];
          return newCustom;
        });
      }
    }

    onHoursChange({
      ...hours,
      [day]: value
    });
  };

  const handleCustomTimeChange = (day: string, type: 'open' | 'close', time: string) => {
    const newCustomHours = {
      ...customHours,
      [day]: {
        ...customHours[day],
        [type]: time
      }
    };
    setCustomHours(newCustomHours);

    // Update the actual hours value
    const customTime = `${newCustomHours[day].open} - ${newCustomHours[day].close}`;
    onHoursChange({
      ...hours,
      [day]: customTime
    });
  };

  const applyBulkHours = () => {
    const newHours = { ...hours };
    DAYS.forEach(({ key }) => {
      newHours[key as keyof BusinessHours] = bulkHours;
    });
    onHoursChange(newHours);
    setBulkEditMode(false);
  };

  const copyPreviousDay = (currentDay: string) => {
    const currentIndex = DAYS.findIndex(d => d.key === currentDay);
    if (currentIndex > 0) {
      const previousDay = DAYS[currentIndex - 1].key;
      const previousHours = hours[previousDay as keyof BusinessHours];
      if (previousHours) {
        handleHourChange(currentDay, previousHours);
      }
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Business Hours</h3>
        <button
          type="button"
          onClick={() => setBulkEditMode(!bulkEditMode)}
          className="text-sm text-green-600 hover:text-green-700 font-medium"
        >
          {bulkEditMode ? "Cancel Bulk Edit" : "Bulk Edit"}
        </button>
      </div>

      {bulkEditMode && (
        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
          <div className="flex items-center space-x-4">
            <label className="text-sm font-medium text-gray-700">
              Apply to all days:
            </label>
            <select
              value={bulkHours}
              onChange={(e) => setBulkHours(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              {PRESET_HOURS.filter(h => h.value !== "Custom").map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <button
              type="button"
              onClick={applyBulkHours}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              Apply
            </button>
          </div>
        </div>
      )}

      <div className="space-y-3">
        {DAYS.map(({ key, label }, index) => (
          <div key={key} className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-2 sm:gap-4 items-start sm:items-center p-3 sm:p-0 border sm:border-0 rounded-lg sm:rounded-none bg-gray-50 sm:bg-transparent">
            <label className="text-sm font-medium text-gray-700 sm:text-right">
              {label}
            </label>
            
            <select
              value={hours[key as keyof BusinessHours] || "Closed"}
              onChange={(e) => handleHourChange(key, e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              {PRESET_HOURS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>

            {hours[key as keyof BusinessHours] === "Custom" && (
              <div className="md:col-span-2 flex items-center space-x-2">
                <select
                  value={customHours[key]?.open || "9:00 AM"}
                  onChange={(e) => handleCustomTimeChange(key, 'open', e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  {TIME_OPTIONS.map(time => (
                    <option key={time} value={time}>{time}</option>
                  ))}
                </select>
                <span className="text-gray-500">to</span>
                <select
                  value={customHours[key]?.close || "5:00 PM"}
                  onChange={(e) => handleCustomTimeChange(key, 'close', e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  {TIME_OPTIONS.map(time => (
                    <option key={time} value={time}>{time}</option>
                  ))}
                </select>
              </div>
            )}

            {!bulkEditMode && index > 0 && (
              <button
                type="button"
                onClick={() => copyPreviousDay(key)}
                className="text-xs text-gray-500 hover:text-gray-700 underline"
              >
                Copy from {DAYS[index - 1].label}
              </button>
            )}
          </div>
        ))}
      </div>

      {showSpecialHours && (
        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <h4 className="text-sm font-medium text-yellow-800 mb-2">
            Special Hours & Temporary Status
          </h4>
          <p className="text-sm text-yellow-700 mb-3">
            Set temporary closures, holiday hours, or special announcements that will override regular hours.
          </p>
          <button
            type="button"
            className="text-sm text-yellow-800 hover:text-yellow-900 font-medium underline"
          >
            Manage Special Hours
          </button>
        </div>
      )}
    </div>
  );
}
