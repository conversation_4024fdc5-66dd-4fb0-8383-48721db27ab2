import { useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";

interface AddBusinessProps {
  onBack: () => void;
  onSuccess: () => void;
}

const PAYMENT_METHODS = [
  "Cash", "Credit Card", "Debit Card", "PayPal", "Venmo", "Zelle", 
  "Apple Pay", "Google Pay", "Check", "Bank Transfer", "Cryptocurrency"
];

const LANGUAGES = [
  "English", "Arabic", "Urdu", "Turkish", "Persian/Farsi", "Bengali", 
  "Indonesian", "Malay", "French", "Spanish", "German", "Russian", 
  "Chinese", "Hindi", "Punjabi", "Somali", "Albanian", "Bosnian"
];

const HOURS_OPTIONS = [
  "Closed",
  "6:00 AM - 6:00 PM",
  "7:00 AM - 7:00 PM", 
  "8:00 AM - 8:00 PM",
  "9:00 AM - 5:00 PM",
  "9:00 AM - 6:00 PM",
  "9:00 AM - 9:00 PM",
  "10:00 AM - 6:00 PM",
  "10:00 AM - 8:00 PM",
  "24 Hours",
  "Custom"
];

export function AddBusiness({ onBack, onSuccess }: AddBusinessProps) {
  const categories = useQuery(api.categories.list);
  const createBusiness = useMutation(api.businesses.create);
  
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    categoryId: "",
    subcategoryIds: [] as string[],
    address: "",
    city: "",
    state: "",
    zipCode: "",
    phone: "",
    email: "",
    website: "",
    services: "",
    paymentMethods: ["Cash", "Credit Card"] as string[],
    languagesSpoken: ["English"] as string[],
    hours: {
      monday: "9:00 AM - 5:00 PM",
      tuesday: "9:00 AM - 5:00 PM",
      wednesday: "9:00 AM - 5:00 PM",
      thursday: "9:00 AM - 5:00 PM",
      friday: "9:00 AM - 5:00 PM",
      saturday: "10:00 AM - 4:00 PM",
      sunday: "Closed",
    },
    customHours: {
      monday: "",
      tuesday: "",
      wednesday: "",
      thursday: "",
      friday: "",
      saturday: "",
      sunday: "",
    },
    isMuslimOwned: true,
    isHalalCertified: false,
    hasPrayerSpace: false,
    hasParking: true,
    isAccessible: true,
    yearsInBusiness: undefined as number | undefined,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const services = formData.services
        .split(",")
        .map(s => s.trim())
        .filter(s => s.length > 0);

      // Process hours - use custom hours if specified
      const processedHours = Object.entries(formData.hours).reduce((acc, [day, time]) => {
        if (time === "Custom") {
          acc[day as keyof typeof acc] = formData.customHours[day as keyof typeof formData.customHours] || "Closed";
        } else {
          acc[day as keyof typeof acc] = time;
        }
        return acc;
      }, {} as typeof formData.hours);

      await createBusiness({
        ...formData,
        services,
        hours: processedHours,
        categoryId: formData.categoryId as any,
        subcategoryIds: formData.subcategoryIds as any[],
      });

      toast.success("Business added successfully!");
      onSuccess();
    } catch (error) {
      toast.error("Failed to add business. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === "checkbox" ? (e.target as HTMLInputElement).checked : 
               type === "number" ? (value ? Number(value) : undefined) : value
    }));
  };

  const handleHoursChange = (day: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      hours: {
        ...prev.hours,
        [day]: value
      }
    }));
  };

  const handleCustomHoursChange = (day: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      customHours: {
        ...prev.customHours,
        [day]: value
      }
    }));
  };

  const handleSubcategoryToggle = (subcategoryId: string) => {
    setFormData(prev => ({
      ...prev,
      subcategoryIds: prev.subcategoryIds.includes(subcategoryId)
        ? prev.subcategoryIds.filter(id => id !== subcategoryId)
        : [...prev.subcategoryIds, subcategoryId]
    }));
  };

  const handlePaymentMethodToggle = (method: string) => {
    setFormData(prev => ({
      ...prev,
      paymentMethods: prev.paymentMethods.includes(method)
        ? prev.paymentMethods.filter(m => m !== method)
        : [...prev.paymentMethods, method]
    }));
  };

  const handleLanguageToggle = (language: string) => {
    setFormData(prev => ({
      ...prev,
      languagesSpoken: prev.languagesSpoken.includes(language)
        ? prev.languagesSpoken.filter(l => l !== language)
        : [...prev.languagesSpoken, language]
    }));
  };

  if (!categories) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  const selectedCategory = categories.find(cat => cat._id === formData.categoryId);
  const availableSubcategories = selectedCategory?.subcategories || [];

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <button
          onClick={onBack}
          className="text-green-600 hover:text-green-700 mb-4 flex items-center"
        >
          ← Back to Home
        </button>
        <h1 className="text-3xl font-bold text-gray-900">Add Your Business</h1>
        <p className="text-gray-600 mt-2">
          Join our directory and connect with the Muslim community
        </p>
      </div>

      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-lg p-8 space-y-8">
        {/* Basic Information */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Basic Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Business Name *
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Years in Business
              </label>
              <input
                type="number"
                name="yearsInBusiness"
                value={formData.yearsInBusiness || ""}
                onChange={handleInputChange}
                min="0"
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description *
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              required
              rows={4}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              placeholder="Describe your business, what makes it special, and what services you offer..."
            />
          </div>
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Services (comma-separated)
            </label>
            <input
              type="text"
              name="services"
              value={formData.services}
              onChange={handleInputChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              placeholder="e.g., Plumbing, Electrical, Emergency Repairs"
            />
          </div>
        </div>

        {/* Categories */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Categories</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Main Category *
              </label>
              <select
                name="categoryId"
                value={formData.categoryId}
                onChange={handleInputChange}
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="">Select a main category</option>
                {categories.map(cat => (
                  <option key={cat._id} value={cat._id}>
                    {cat.name}
                  </option>
                ))}
              </select>
            </div>
            
            {availableSubcategories.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Sub-categories (select all that apply)
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {availableSubcategories.map(subcat => (
                    <label key={subcat._id} className="flex items-center space-x-2 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={formData.subcategoryIds.includes(subcat._id)}
                        onChange={() => handleSubcategoryToggle(subcat._id)}
                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                      />
                      <span className="text-sm text-gray-700">{subcat.name}</span>
                    </label>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Location */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Location</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Street Address *
              </label>
              <input
                type="text"
                name="address"
                value={formData.address}
                onChange={handleInputChange}
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  City *
                </label>
                <input
                  type="text"
                  name="city"
                  value={formData.city}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  State *
                </label>
                <input
                  type="text"
                  name="state"
                  value={formData.state}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  ZIP Code *
                </label>
                <input
                  type="text"
                  name="zipCode"
                  value={formData.zipCode}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Contact Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Phone Number
              </label>
              <input
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Address
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Website
              </label>
              <input
                type="url"
                name="website"
                value={formData.website}
                onChange={handleInputChange}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="https://"
              />
            </div>
          </div>
        </div>

        {/* Business Hours */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Business Hours</h2>
          <div className="space-y-3">
            {Object.entries(formData.hours).map(([day, hours]) => (
              <div key={day} className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
                <label className="text-sm font-medium text-gray-700 capitalize">
                  {day}
                </label>
                <select
                  value={hours}
                  onChange={(e) => handleHoursChange(day, e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  {HOURS_OPTIONS.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
                {hours === "Custom" && (
                  <input
                    type="text"
                    placeholder="e.g., 8:00 AM - 6:00 PM"
                    value={formData.customHours[day as keyof typeof formData.customHours]}
                    onChange={(e) => handleCustomHoursChange(day, e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Payment Methods */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Payment Methods</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {PAYMENT_METHODS.map(method => (
              <label key={method} className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.paymentMethods.includes(method)}
                  onChange={() => handlePaymentMethodToggle(method)}
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <span className="text-sm text-gray-700">{method}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Languages Spoken */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Languages Spoken</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {LANGUAGES.map(language => (
              <label key={language} className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.languagesSpoken.includes(language)}
                  onChange={() => handleLanguageToggle(language)}
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <span className="text-sm text-gray-700">{language}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Business Attributes */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Business Attributes</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                name="isMuslimOwned"
                checked={formData.isMuslimOwned}
                onChange={handleInputChange}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-900">
                Muslim-Owned Business
              </label>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                name="isHalalCertified"
                checked={formData.isHalalCertified}
                onChange={handleInputChange}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-900">
                Halal Certified (for food businesses)
              </label>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                name="hasPrayerSpace"
                checked={formData.hasPrayerSpace}
                onChange={handleInputChange}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-900">
                Prayer Space Available
              </label>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                name="hasParking"
                checked={formData.hasParking}
                onChange={handleInputChange}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-900">
                Parking Available
              </label>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                name="isAccessible"
                checked={formData.isAccessible}
                onChange={handleInputChange}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-900">
                Wheelchair Accessible
              </label>
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onBack}
            className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? "Adding Business..." : "Add Business"}
          </button>
        </div>
      </form>
    </div>
  );
}
