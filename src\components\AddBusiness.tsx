import { useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";
import { MediaUpload } from "./MediaUpload";
import { BusinessHoursManager } from "./BusinessHoursManager";
import { Id } from "../../convex/_generated/dataModel";

interface AddBusinessProps {
  onBack: () => void;
  onSuccess: () => void;
}

const PAYMENT_METHODS = [
  "Cash", "Credit Card", "Debit Card", "PayPal", "Venmo", "Zelle", 
  "Apple Pay", "Google Pay", "Check", "Bank Transfer", "Cryptocurrency"
];

const LANGUAGES = [
  "English", "Arabic", "Urdu", "Turkish", "Persian/Farsi", "Bengali", 
  "Indonesian", "Malay", "French", "Spanish", "German", "Russian", 
  "Chinese", "Hindi", "Punjabi", "Somali", "Albanian", "Bosnian"
];

const HOURS_OPTIONS = [
  "Closed",
  "6:00 AM - 6:00 PM",
  "7:00 AM - 7:00 PM", 
  "8:00 AM - 8:00 PM",
  "9:00 AM - 5:00 PM",
  "9:00 AM - 6:00 PM",
  "9:00 AM - 9:00 PM",
  "10:00 AM - 6:00 PM",
  "10:00 AM - 8:00 PM",
  "24 Hours",
  "Custom"
];

export function AddBusiness({ onBack, onSuccess }: AddBusinessProps) {
  const categories = useQuery(api.categories.list);
  const createBusiness = useMutation(api.businesses.create);
  
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    categoryId: "",
    subcategoryIds: [] as string[],
    address: "",
    city: "",
    state: "",
    zipCode: "",
    phone: "",
    email: "",
    website: "",
    services: "",
    paymentMethods: ["Cash", "Credit Card"] as string[],
    languagesSpoken: ["English"] as string[],
    hours: {
      monday: "9:00 AM - 5:00 PM",
      tuesday: "9:00 AM - 5:00 PM",
      wednesday: "9:00 AM - 5:00 PM",
      thursday: "9:00 AM - 5:00 PM",
      friday: "9:00 AM - 5:00 PM",
      saturday: "10:00 AM - 4:00 PM",
      sunday: "Closed",
    },
    customHours: {
      monday: "",
      tuesday: "",
      wednesday: "",
      thursday: "",
      friday: "",
      saturday: "",
      sunday: "",
    },
    isMuslimOwned: true,
    isHalalCertified: false,
    hasPrayerSpace: false,
    hasParking: true,
    isAccessible: true,
    yearsInBusiness: undefined as number | undefined,
    facebookUrl: "",
    instagramUrl: "",
    twitterUrl: "",
    linkedinUrl: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedMedia, setUploadedMedia] = useState<{
    images: Id<"_storage">[];
    videos: Id<"_storage">[];
  }>({ images: [], videos: [] });
  const [currentStep, setCurrentStep] = useState(1);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const errors: Record<string, string> = {};

    // Required fields validation
    if (!formData.name.trim()) {
      errors.name = "Business name is required";
    } else if (formData.name.length < 2) {
      errors.name = "Business name must be at least 2 characters";
    } else if (formData.name.length > 100) {
      errors.name = "Business name must be less than 100 characters";
    }

    if (!formData.description.trim()) {
      errors.description = "Description is required";
    } else if (formData.description.length < 20) {
      errors.description = "Description must be at least 20 characters";
    } else if (formData.description.length > 1000) {
      errors.description = "Description must be less than 1000 characters";
    }

    if (!formData.categoryId) errors.categoryId = "Category is required";

    if (!formData.address.trim()) {
      errors.address = "Address is required";
    } else if (formData.address.length < 5) {
      errors.address = "Please enter a complete address";
    }

    if (!formData.city.trim()) {
      errors.city = "City is required";
    } else if (formData.city.length < 2) {
      errors.city = "Please enter a valid city name";
    }

    if (!formData.state.trim()) {
      errors.state = "State is required";
    } else if (formData.state.length < 2) {
      errors.state = "Please enter a valid state";
    }

    if (!formData.zipCode.trim()) {
      errors.zipCode = "ZIP code is required";
    } else if (!/^\d{5}(-\d{4})?$/.test(formData.zipCode)) {
      errors.zipCode = "Please enter a valid ZIP code (e.g., 12345 or 12345-6789)";
    }

    // Email validation
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = "Please enter a valid email address";
    }

    // Website validation
    if (formData.website) {
      if (!formData.website.startsWith('http://') && !formData.website.startsWith('https://')) {
        errors.website = "Website must start with http:// or https://";
      } else {
        try {
          new URL(formData.website);
        } catch {
          errors.website = "Please enter a valid website URL";
        }
      }
    }

    // Phone validation
    if (formData.phone) {
      const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
      const cleanPhone = formData.phone.replace(/[\s\-\(\)]/g, '');
      if (!phoneRegex.test(cleanPhone)) {
        errors.phone = "Please enter a valid phone number";
      }
    }

    // Social media URL validation
    if (formData.facebookUrl) {
      try {
        const url = new URL(formData.facebookUrl);
        if (!url.hostname.includes('facebook.com')) {
          errors.facebookUrl = "Please enter a valid Facebook URL";
        }
      } catch {
        errors.facebookUrl = "Please enter a valid Facebook URL";
      }
    }

    if (formData.instagramUrl) {
      try {
        const url = new URL(formData.instagramUrl);
        if (!url.hostname.includes('instagram.com')) {
          errors.instagramUrl = "Please enter a valid Instagram URL";
        }
      } catch {
        errors.instagramUrl = "Please enter a valid Instagram URL";
      }
    }

    if (formData.twitterUrl) {
      try {
        const url = new URL(formData.twitterUrl);
        if (!url.hostname.includes('twitter.com') && !url.hostname.includes('x.com')) {
          errors.twitterUrl = "Please enter a valid Twitter/X URL";
        }
      } catch {
        errors.twitterUrl = "Please enter a valid Twitter/X URL";
      }
    }

    if (formData.linkedinUrl) {
      try {
        const url = new URL(formData.linkedinUrl);
        if (!url.hostname.includes('linkedin.com')) {
          errors.linkedinUrl = "Please enter a valid LinkedIn URL";
        }
      } catch {
        errors.linkedinUrl = "Please enter a valid LinkedIn URL";
      }
    }

    // Services validation
    if (formData.services.trim()) {
      const services = formData.services.split(',').map(s => s.trim()).filter(s => s.length > 0);
      if (services.length === 0) {
        errors.services = "Please enter at least one service";
      } else if (services.some(s => s.length < 2)) {
        errors.services = "Each service must be at least 2 characters";
      }
    }

    // Years in business validation
    if (formData.yearsInBusiness !== undefined) {
      if (formData.yearsInBusiness < 0) {
        errors.yearsInBusiness = "Years in business cannot be negative";
      } else if (formData.yearsInBusiness > 200) {
        errors.yearsInBusiness = "Please enter a realistic number of years";
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleMediaUploaded = (mediaId: Id<"_storage">, mediaType: "image" | "video") => {
    setUploadedMedia(prev => ({
      ...prev,
      [mediaType === "image" ? "images" : "videos"]: [
        ...prev[mediaType === "image" ? "images" : "videos"],
        mediaId
      ]
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error("Please fix the errors in the form");
      return;
    }

    setIsSubmitting(true);

    try {
      const services = formData.services
        .split(",")
        .map(s => s.trim())
        .filter(s => s.length > 0);

      // Process hours - use custom hours if specified
      const processedHours = Object.entries(formData.hours).reduce((acc, [day, time]) => {
        if (time === "Custom") {
          acc[day as keyof typeof acc] = formData.customHours[day as keyof typeof formData.customHours] || "Closed";
        } else {
          acc[day as keyof typeof acc] = time;
        }
        return acc;
      }, {} as typeof formData.hours);

      // Create business with additional fields
      const businessData = {
        ...formData,
        services,
        hours: processedHours,
        categoryId: formData.categoryId as any,
        subcategoryIds: formData.subcategoryIds as any[],
        // Remove empty optional fields
        phone: formData.phone || undefined,
        email: formData.email || undefined,
        website: formData.website || undefined,
        facebookUrl: formData.facebookUrl || undefined,
        instagramUrl: formData.instagramUrl || undefined,
        twitterUrl: formData.twitterUrl || undefined,
        linkedinUrl: formData.linkedinUrl || undefined,
      };

      await createBusiness(businessData);

      toast.success("Business added successfully!");
      onSuccess();
    } catch (error) {
      console.error("Error creating business:", error);
      toast.error("Failed to add business. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === "checkbox" ? (e.target as HTMLInputElement).checked : 
               type === "number" ? (value ? Number(value) : undefined) : value
    }));
  };

  const handleHoursChange = (day: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      hours: {
        ...prev.hours,
        [day]: value
      }
    }));
  };

  const handleCustomHoursChange = (day: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      customHours: {
        ...prev.customHours,
        [day]: value
      }
    }));
  };

  const handleSubcategoryToggle = (subcategoryId: string) => {
    setFormData(prev => ({
      ...prev,
      subcategoryIds: prev.subcategoryIds.includes(subcategoryId)
        ? prev.subcategoryIds.filter(id => id !== subcategoryId)
        : [...prev.subcategoryIds, subcategoryId]
    }));
  };

  const handlePaymentMethodToggle = (method: string) => {
    setFormData(prev => ({
      ...prev,
      paymentMethods: prev.paymentMethods.includes(method)
        ? prev.paymentMethods.filter(m => m !== method)
        : [...prev.paymentMethods, method]
    }));
  };

  const handleLanguageToggle = (language: string) => {
    setFormData(prev => ({
      ...prev,
      languagesSpoken: prev.languagesSpoken.includes(language)
        ? prev.languagesSpoken.filter(l => l !== language)
        : [...prev.languagesSpoken, language]
    }));
  };

  if (!categories) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  const selectedCategory = categories.find(cat => cat._id === formData.categoryId);
  const availableSubcategories = selectedCategory?.subcategories || [];

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <button
          onClick={onBack}
          className="text-green-600 hover:text-green-700 mb-4 flex items-center"
        >
          ← Back to Home
        </button>
        <h1 className="text-3xl font-bold text-gray-900">Add Your Business</h1>
        <p className="text-gray-600 mt-2 mb-4">
          Join our directory and connect with the Muslim community
        </p>

        {/* Progress Indicator */}
        <div className="bg-white rounded-lg p-4 shadow-sm border">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Complete all required fields (*) to submit</span>
            <span className={`font-medium ${Object.keys(formErrors).length === 0 ? "text-green-600" : "text-red-600"}`}>
              {Object.keys(formErrors).length === 0 ? "✓ Ready to submit" : `${Object.keys(formErrors).length} errors to fix`}
            </span>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-lg p-8 space-y-8">
        {/* Basic Information */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Basic Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Business Name *
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent ${
                  formErrors.name ? "border-red-500" : "border-gray-300"
                }`}
              />
              {formErrors.name && (
                <p className="mt-1 text-sm text-red-600">{formErrors.name}</p>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Years in Business
              </label>
              <input
                type="number"
                name="yearsInBusiness"
                value={formData.yearsInBusiness || ""}
                onChange={handleInputChange}
                min="0"
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description *
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              required
              rows={4}
              className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent ${
                formErrors.description ? "border-red-500" : "border-gray-300"
              }`}
              placeholder="Describe your business, what makes it special, and what services you offer..."
            />
            {formErrors.description && (
              <p className="mt-1 text-sm text-red-600">{formErrors.description}</p>
            )}
          </div>
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Services (comma-separated)
            </label>
            <input
              type="text"
              name="services"
              value={formData.services}
              onChange={handleInputChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              placeholder="e.g., Plumbing, Electrical, Emergency Repairs"
            />
          </div>
        </div>

        {/* Categories */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Categories</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Main Category *
              </label>
              <select
                name="categoryId"
                value={formData.categoryId}
                onChange={handleInputChange}
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="">Select a main category</option>
                {categories.map(cat => (
                  <option key={cat._id} value={cat._id}>
                    {cat.name}
                  </option>
                ))}
              </select>
            </div>
            
            {availableSubcategories.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Sub-categories (select all that apply)
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {availableSubcategories.map(subcat => (
                    <label key={subcat._id} className="flex items-center space-x-2 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={formData.subcategoryIds.includes(subcat._id)}
                        onChange={() => handleSubcategoryToggle(subcat._id)}
                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                      />
                      <span className="text-sm text-gray-700">{subcat.name}</span>
                    </label>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Location */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Location</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Street Address *
              </label>
              <input
                type="text"
                name="address"
                value={formData.address}
                onChange={handleInputChange}
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  City *
                </label>
                <input
                  type="text"
                  name="city"
                  value={formData.city}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  State *
                </label>
                <input
                  type="text"
                  name="state"
                  value={formData.state}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  ZIP Code *
                </label>
                <input
                  type="text"
                  name="zipCode"
                  value={formData.zipCode}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Contact Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Phone Number
              </label>
              <input
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Address
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent ${
                  formErrors.email ? "border-red-500" : "border-gray-300"
                }`}
              />
              {formErrors.email && (
                <p className="mt-1 text-sm text-red-600">{formErrors.email}</p>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Website
              </label>
              <input
                type="url"
                name="website"
                value={formData.website}
                onChange={handleInputChange}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="https://"
              />
            </div>
          </div>
        </div>

        {/* Business Hours */}
        <div>
          <BusinessHoursManager
            hours={formData.hours}
            onHoursChange={(hours) => setFormData(prev => ({ ...prev, hours }))}
            showSpecialHours={false}
          />
        </div>

        {/* Payment Methods */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Payment Methods</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {PAYMENT_METHODS.map(method => (
              <label key={method} className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.paymentMethods.includes(method)}
                  onChange={() => handlePaymentMethodToggle(method)}
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <span className="text-sm text-gray-700">{method}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Languages Spoken */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Languages Spoken</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {LANGUAGES.map(language => (
              <label key={language} className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.languagesSpoken.includes(language)}
                  onChange={() => handleLanguageToggle(language)}
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <span className="text-sm text-gray-700">{language}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Business Attributes */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Business Attributes</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                name="isMuslimOwned"
                checked={formData.isMuslimOwned}
                onChange={handleInputChange}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-900">
                Muslim-Owned Business
              </label>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                name="isHalalCertified"
                checked={formData.isHalalCertified}
                onChange={handleInputChange}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-900">
                Halal Certified (for food businesses)
              </label>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                name="hasPrayerSpace"
                checked={formData.hasPrayerSpace}
                onChange={handleInputChange}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-900">
                Prayer Space Available
              </label>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                name="hasParking"
                checked={formData.hasParking}
                onChange={handleInputChange}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-900">
                Parking Available
              </label>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                name="isAccessible"
                checked={formData.isAccessible}
                onChange={handleInputChange}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-900">
                Wheelchair Accessible
              </label>
            </div>
          </div>
        </div>

        {/* Social Media */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Social Media (Optional)</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Facebook URL
              </label>
              <input
                type="url"
                name="facebookUrl"
                value={formData.facebookUrl}
                onChange={handleInputChange}
                placeholder="https://facebook.com/yourbusiness"
                className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent ${
                  formErrors.facebookUrl ? "border-red-500" : "border-gray-300"
                }`}
              />
              {formErrors.facebookUrl && (
                <p className="mt-1 text-sm text-red-600">{formErrors.facebookUrl}</p>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Instagram URL
              </label>
              <input
                type="url"
                name="instagramUrl"
                value={formData.instagramUrl}
                onChange={handleInputChange}
                placeholder="https://instagram.com/yourbusiness"
                className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent ${
                  formErrors.instagramUrl ? "border-red-500" : "border-gray-300"
                }`}
              />
              {formErrors.instagramUrl && (
                <p className="mt-1 text-sm text-red-600">{formErrors.instagramUrl}</p>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Twitter URL
              </label>
              <input
                type="url"
                name="twitterUrl"
                value={formData.twitterUrl}
                onChange={handleInputChange}
                placeholder="https://twitter.com/yourbusiness"
                className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent ${
                  formErrors.twitterUrl ? "border-red-500" : "border-gray-300"
                }`}
              />
              {formErrors.twitterUrl && (
                <p className="mt-1 text-sm text-red-600">{formErrors.twitterUrl}</p>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                LinkedIn URL
              </label>
              <input
                type="url"
                name="linkedinUrl"
                value={formData.linkedinUrl}
                onChange={handleInputChange}
                placeholder="https://linkedin.com/company/yourbusiness"
                className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent ${
                  formErrors.linkedinUrl ? "border-red-500" : "border-gray-300"
                }`}
              />
              {formErrors.linkedinUrl && (
                <p className="mt-1 text-sm text-red-600">{formErrors.linkedinUrl}</p>
              )}
            </div>
          </div>
        </div>

        {/* Media Upload */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Photos & Videos (Optional)</h2>
          <p className="text-sm text-gray-600 mb-4">
            Add photos and videos to showcase your business. You can upload up to 10 files.
          </p>
          <MediaUpload
            onMediaUploaded={handleMediaUploaded}
            maxFiles={10}
            showPreview={true}
          />
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onBack}
            className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? "Adding Business..." : "Add Business"}
          </button>
        </div>
      </form>
    </div>
  );
}
