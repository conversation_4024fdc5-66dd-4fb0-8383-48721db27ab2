import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useState, useEffect } from "react";
import { SearchBar } from "./SearchBar";

interface BusinessDirectoryProps {
  categorySlug: string;
  onBusinessSelect: (slug: string) => void;
  onBack: () => void;
  onCategorySelect?: (slug: string) => void;
}

export function BusinessDirectory({ categorySlug, onBusinessSelect, onBack, onCategorySelect }: BusinessDirectoryProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCity, setSelectedCity] = useState("");
  const [sortBy, setSortBy] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const businesses = useQuery(api.businesses.list, {
    categorySlug: categorySlug || undefined,
    search: searchTerm || undefined,
    city: selectedCity || undefined,
    sortBy: sortBy || undefined,
  });

  const categories = useQuery(api.categories.list);

  // Handle search suggestion selection
  const handleSuggestionSelect = (suggestion: any) => {
    if (suggestion.type === "category" && onCategorySelect) {
      onCategorySelect(suggestion.slug);
    } else if (suggestion.type === "business") {
      onBusinessSelect(suggestion.slug);
    }
  };

  // Debounced search effect
  useEffect(() => {
    setIsLoading(true);
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm, selectedCity, sortBy]);

  if (!businesses || !categories) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  const currentCategory = categorySlug 
    ? categories.find(cat => cat.slug === categorySlug) ||
      categories.flatMap(cat => cat.subcategories).find(sub => sub.slug === categorySlug)
    : null;

  // Get unique cities for filter
  const cities = [...new Set(businesses.map(b => b.city))].sort();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <button
            onClick={onBack}
            className="text-green-600 hover:text-green-700 mb-2 flex items-center"
          >
            ← Back to Home
          </button>
          <h1 className="text-3xl font-bold text-gray-900">
            {currentCategory ? currentCategory.name : "All Businesses"}
          </h1>
          <p className="text-gray-600 mt-2">
            {businesses.length} businesses found
          </p>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="md:col-span-3">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Search Businesses
            </label>
            <SearchBar
              value={searchTerm}
              onChange={setSearchTerm}
              onSuggestionSelect={handleSuggestionSelect}
              className="w-full"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              City
            </label>
            <select
              value={selectedCity}
              onChange={(e) => setSelectedCity(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              <option value="">All Cities</option>
              {cities.map(city => (
                <option key={city} value={city}>{city}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Sort by
            </label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              <option value="">Default</option>
              <option value="name">Name (A-Z)</option>
              <option value="rating">Highest Rated</option>
              <option value="reviews">Most Reviews</option>
              <option value="newest">Newest First</option>
            </select>
          </div>
        </div>

        <div className="flex justify-between items-center pt-4 border-t">
          <div className="text-sm text-gray-600">
            {isLoading ? "Searching..." : `${businesses.length} businesses found`}
          </div>
          <button
            onClick={() => {
              setSearchTerm("");
              setSelectedCity("");
              setSortBy("");
            }}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm"
          >
            Clear All Filters
          </button>
        </div>
      </div>

      {/* Business Listings */}
      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
        </div>
      ) : businesses.length === 0 ? (
        <div className="text-center py-12">
          <div className="max-w-md mx-auto">
            <div className="mb-4">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No businesses found</h3>
            <p className="text-gray-600 mb-6">
              {searchTerm || selectedCity ?
                "Try adjusting your search criteria or filters." :
                categorySlug ?
                  "No businesses are currently listed in this category." :
                  "No businesses are currently available."
              }
            </p>
            {(searchTerm || selectedCity) && (
              <button
                onClick={() => {
                  setSearchTerm("");
                  setSelectedCity("");
                  setSortBy("");
                }}
                className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
              >
                Clear Filters
              </button>
            )}
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {businesses.map((business) => (
          <div
            key={business._id}
            className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 cursor-pointer"
            onClick={() => onBusinessSelect(business.slug)}
          >
            <div className="flex items-start space-x-4">
              {business.logoUrl && (
                <img
                  src={business.logoUrl}
                  alt={business.name}
                  className="w-20 h-20 rounded-lg object-cover flex-shrink-0"
                />
              )}
              <div className="flex-1 min-w-0">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {business.name}
                </h3>
                <p className="text-gray-600 mb-3 line-clamp-2">
                  {business.description}
                </p>
                
                <div className="space-y-2 text-sm text-gray-500">
                  <div className="flex items-center">
                    <span className="w-4 h-4 mr-2">📍</span>
                    {business.address}, {business.city}, {business.state}
                  </div>
                  {business.phone && (
                    <div className="flex items-center">
                      <span className="w-4 h-4 mr-2">📞</span>
                      {business.phone}
                    </div>
                  )}
                  <div className="flex items-center">
                    <span className="w-4 h-4 mr-2">🏷️</span>
                    {business.category?.name}
                  </div>
                </div>

                <div className="flex items-center justify-between mt-4">
                  <div className="flex flex-wrap gap-2">
                    {business.isMuslimOwned && (
                      <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                        Muslim-Owned
                      </span>
                    )}
                    {business.isHalalCertified && (
                      <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                        Halal Certified
                      </span>
                    )}
                    {business.isVerified && (
                      <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs font-medium">
                        Verified
                      </span>
                    )}
                    {business.hasPrayerSpace && (
                      <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">
                        Prayer Space
                      </span>
                    )}
                  </div>
                  
                  {business.averageRating > 0 && (
                    <div className="flex items-center">
                      <span className="text-yellow-400 mr-1">★</span>
                      <span className="text-gray-600 text-sm">
                        {business.averageRating.toFixed(1)} ({business.reviewCount})
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      )}
    </div>
  );
}
