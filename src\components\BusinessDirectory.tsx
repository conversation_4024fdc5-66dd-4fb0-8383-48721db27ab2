import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useState } from "react";

interface BusinessDirectoryProps {
  categorySlug: string;
  onBusinessSelect: (slug: string) => void;
  onBack: () => void;
}

export function BusinessDirectory({ categorySlug, onBusinessSelect, onBack }: BusinessDirectoryProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCity, setSelectedCity] = useState("");
  
  const businesses = useQuery(api.businesses.list, {
    categorySlug: categorySlug || undefined,
    search: searchTerm || undefined,
    city: selectedCity || undefined,
  });

  const categories = useQuery(api.categories.list);

  if (!businesses || !categories) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  const currentCategory = categorySlug 
    ? categories.find(cat => cat.slug === categorySlug) ||
      categories.flatMap(cat => cat.subcategories).find(sub => sub.slug === categorySlug)
    : null;

  // Get unique cities for filter
  const cities = [...new Set(businesses.map(b => b.city))].sort();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <button
            onClick={onBack}
            className="text-green-600 hover:text-green-700 mb-2 flex items-center"
          >
            ← Back to Home
          </button>
          <h1 className="text-3xl font-bold text-gray-900">
            {currentCategory ? currentCategory.name : "All Businesses"}
          </h1>
          <p className="text-gray-600 mt-2">
            {businesses.length} businesses found
          </p>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Search Businesses
            </label>
            <input
              type="text"
              placeholder="Search by name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              City
            </label>
            <select
              value={selectedCity}
              onChange={(e) => setSelectedCity(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              <option value="">All Cities</option>
              {cities.map(city => (
                <option key={city} value={city}>{city}</option>
              ))}
            </select>
          </div>
          <div className="flex items-end">
            <button
              onClick={() => {
                setSearchTerm("");
                setSelectedCity("");
              }}
              className="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Business Listings */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {businesses.map((business) => (
          <div
            key={business._id}
            className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 cursor-pointer"
            onClick={() => onBusinessSelect(business.slug)}
          >
            <div className="flex items-start space-x-4">
              {business.logoUrl && (
                <img
                  src={business.logoUrl}
                  alt={business.name}
                  className="w-20 h-20 rounded-lg object-cover flex-shrink-0"
                />
              )}
              <div className="flex-1 min-w-0">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {business.name}
                </h3>
                <p className="text-gray-600 mb-3 line-clamp-2">
                  {business.description}
                </p>
                
                <div className="space-y-2 text-sm text-gray-500">
                  <div className="flex items-center">
                    <span className="w-4 h-4 mr-2">📍</span>
                    {business.address}, {business.city}, {business.state}
                  </div>
                  {business.phone && (
                    <div className="flex items-center">
                      <span className="w-4 h-4 mr-2">📞</span>
                      {business.phone}
                    </div>
                  )}
                  <div className="flex items-center">
                    <span className="w-4 h-4 mr-2">🏷️</span>
                    {business.category?.name}
                  </div>
                </div>

                <div className="flex items-center justify-between mt-4">
                  <div className="flex flex-wrap gap-2">
                    {business.isMuslimOwned && (
                      <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                        Muslim-Owned
                      </span>
                    )}
                    {business.isHalalCertified && (
                      <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                        Halal Certified
                      </span>
                    )}
                    {business.isVerified && (
                      <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs font-medium">
                        Verified
                      </span>
                    )}
                    {business.hasPrayerSpace && (
                      <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">
                        Prayer Space
                      </span>
                    )}
                  </div>
                  
                  {business.averageRating > 0 && (
                    <div className="flex items-center">
                      <span className="text-yellow-400 mr-1">★</span>
                      <span className="text-gray-600 text-sm">
                        {business.averageRating.toFixed(1)} ({business.reviewCount})
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {businesses.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">🔍</div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            No businesses found
          </h3>
          <p className="text-gray-600">
            Try adjusting your search criteria or browse different categories.
          </p>
        </div>
      )}
    </div>
  );
}
