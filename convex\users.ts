import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

export const getCurrentUser = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }
    
    const user = await ctx.db.get(userId);
    if (!user) {
      return null;
    }

    return {
      _id: user._id,
      email: user.email,
      name: user.name,
      _creationTime: user._creationTime,
    };
  },
});

export const updateProfile = mutation({
  args: {
    name: v.optional(v.string()),
    email: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Must be logged in to update profile");
    }

    const user = await ctx.db.get(userId);
    if (!user) {
      throw new Error("User not found");
    }

    // Filter out undefined values
    const updateData = Object.fromEntries(
      Object.entries(args).filter(([_, value]) => value !== undefined)
    );

    if (Object.keys(updateData).length === 0) {
      return { success: true };
    }

    await ctx.db.patch(userId, updateData);

    return { success: true };
  },
});

export const getUserStats = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    // Get user's businesses
    const businesses = await ctx.db
      .query("businesses")
      .withIndex("by_owner", (q) => q.eq("ownerId", userId))
      .collect();

    // Get total reviews for user's businesses
    const businessIds = businesses.map(b => b._id);
    let totalReviews = 0;
    let totalRating = 0;

    for (const businessId of businessIds) {
      const reviews = await ctx.db
        .query("reviews")
        .withIndex("by_business", (q) => q.eq("businessId", businessId))
        .collect();
      
      totalReviews += reviews.length;
      totalRating += reviews.reduce((sum, review) => sum + review.rating, 0);
    }

    const averageRating = totalReviews > 0 ? totalRating / totalReviews : 0;

    // Count active vs inactive businesses
    const activeBusinesses = businesses.filter(b => b.isActive).length;
    const inactiveBusinesses = businesses.length - activeBusinesses;

    // Count verified businesses
    const verifiedBusinesses = businesses.filter(b => b.isVerified).length;

    return {
      totalBusinesses: businesses.length,
      activeBusinesses,
      inactiveBusinesses,
      verifiedBusinesses,
      totalReviews,
      averageRating,
    };
  },
});

export const deleteAccount = mutation({
  args: {
    confirmEmail: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Must be logged in to delete account");
    }

    const user = await ctx.db.get(userId);
    if (!user) {
      throw new Error("User not found");
    }

    // Verify email confirmation
    if (user.email !== args.confirmEmail) {
      throw new Error("Email confirmation does not match");
    }

    // Get user's businesses
    const businesses = await ctx.db
      .query("businesses")
      .withIndex("by_owner", (q) => q.eq("ownerId", userId))
      .collect();

    // Delete all user's businesses and their associated data
    for (const business of businesses) {
      // Delete business reviews
      const reviews = await ctx.db
        .query("reviews")
        .withIndex("by_business", (q) => q.eq("businessId", business._id))
        .collect();
      
      for (const review of reviews) {
        await ctx.db.delete(review._id);
      }

      // Delete business claims
      const claims = await ctx.db
        .query("businessClaims")
        .withIndex("by_business", (q) => q.eq("businessId", business._id))
        .collect();
      
      for (const claim of claims) {
        await ctx.db.delete(claim._id);
      }

      // Delete business media files
      for (const imageId of business.imageIds) {
        try {
          await ctx.storage.delete(imageId);
        } catch (error) {
          // Continue if file doesn't exist
        }
      }

      if (business.videoIds) {
        for (const videoId of business.videoIds) {
          try {
            await ctx.storage.delete(videoId);
          } catch (error) {
            // Continue if file doesn't exist
          }
        }
      }

      if (business.logoId) {
        try {
          await ctx.storage.delete(business.logoId);
        } catch (error) {
          // Continue if file doesn't exist
        }
      }

      // Delete the business
      await ctx.db.delete(business._id);
    }

    // Delete user's reviews on other businesses
    const userReviews = await ctx.db
      .query("reviews")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .collect();
    
    for (const review of userReviews) {
      await ctx.db.delete(review._id);
    }

    // Delete user's business claims
    const userClaims = await ctx.db
      .query("businessClaims")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .collect();
    
    for (const claim of userClaims) {
      await ctx.db.delete(claim._id);
    }

    // Finally, delete the user account
    await ctx.db.delete(userId);

    return { success: true };
  },
});
