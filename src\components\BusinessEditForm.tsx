import { useState, useEffect } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";
import { Id } from "../../convex/_generated/dataModel";
import { BusinessHoursManager } from "./BusinessHoursManager";
import { YouTubeInput, isValidYouTubeUrl } from "./YouTubePlayer";

interface BusinessEditFormProps {
  businessId: Id<"businesses">;
  onSuccess: () => void;
  onCancel: () => void;
}

export function BusinessEditForm({ businessId, onSuccess, onCancel }: BusinessEditFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  const business = useQuery(api.businesses.getBySlug, { 
    slug: "" // We'll need to get this differently
  });
  
  const updateBusiness = useMutation(api.businesses.updateBusiness);
  const categories = useQuery(api.categories.list);

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    categoryId: "",
    subcategoryIds: [] as string[],
    address: "",
    city: "",
    state: "",
    zipCode: "",
    phone: "",
    email: "",
    website: "",
    services: "",
    paymentMethods: ["Cash", "Credit Card"] as string[],
    languagesSpoken: ["English"] as string[],
    hours: {
      monday: "9:00 AM - 5:00 PM",
      tuesday: "9:00 AM - 5:00 PM",
      wednesday: "9:00 AM - 5:00 PM",
      thursday: "9:00 AM - 5:00 PM",
      friday: "9:00 AM - 5:00 PM",
      saturday: "10:00 AM - 4:00 PM",
      sunday: "Closed",
    },
    isMuslimOwned: true,
    isHalalCertified: false,
    hasPrayerSpace: false,
    hasParking: true,
    isAccessible: true,
    yearsInBusiness: undefined as number | undefined,
    facebookUrl: "",
    instagramUrl: "",
    twitterUrl: "",
    linkedinUrl: "",
    youtubeVideoUrl: "",
  });

  // Initialize form data when business data loads
  useEffect(() => {
    if (business) {
      setFormData({
        name: business.name || "",
        description: business.description || "",
        categoryId: business.categoryId || "",
        subcategoryIds: business.subcategoryIds || [],
        address: business.address || "",
        city: business.city || "",
        state: business.state || "",
        zipCode: business.zipCode || "",
        phone: business.phone || "",
        email: business.email || "",
        website: business.website || "",
        services: business.services?.join(", ") || "",
        paymentMethods: business.paymentMethods || ["Cash", "Credit Card"],
        languagesSpoken: business.languagesSpoken || ["English"],
        hours: business.hours || {
          monday: "9:00 AM - 5:00 PM",
          tuesday: "9:00 AM - 5:00 PM",
          wednesday: "9:00 AM - 5:00 PM",
          thursday: "9:00 AM - 5:00 PM",
          friday: "9:00 AM - 5:00 PM",
          saturday: "10:00 AM - 4:00 PM",
          sunday: "Closed",
        },
        isMuslimOwned: business.isMuslimOwned ?? true,
        isHalalCertified: business.isHalalCertified ?? false,
        hasPrayerSpace: business.hasPrayerSpace ?? false,
        hasParking: business.hasParking ?? true,
        isAccessible: business.isAccessible ?? true,
        yearsInBusiness: business.yearsInBusiness,
        facebookUrl: business.facebookUrl || "",
        instagramUrl: business.instagramUrl || "",
        twitterUrl: business.twitterUrl || "",
        linkedinUrl: business.linkedinUrl || "",
        youtubeVideoUrl: business.youtubeVideoUrl || "",
      });
    }
  }, [business]);

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) errors.name = "Business name is required";
    if (!formData.description.trim()) errors.description = "Description is required";
    if (!formData.address.trim()) errors.address = "Address is required";
    if (!formData.city.trim()) errors.city = "City is required";
    if (!formData.state.trim()) errors.state = "State is required";
    if (!formData.zipCode.trim()) errors.zipCode = "ZIP code is required";

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = "Please enter a valid email address";
    }

    if (formData.website && !formData.website.startsWith('http')) {
      errors.website = "Website must start with http:// or https://";
    }

    if (formData.youtubeVideoUrl && !isValidYouTubeUrl(formData.youtubeVideoUrl)) {
      errors.youtubeVideoUrl = "Please enter a valid YouTube URL";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === "checkbox") {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else if (type === "number") {
      setFormData(prev => ({ ...prev, [name]: value ? parseInt(value) : undefined }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error("Please fix the errors in the form");
      return;
    }

    setIsSubmitting(true);

    try {
      const services = formData.services
        .split(",")
        .map(s => s.trim())
        .filter(s => s.length > 0);

      await updateBusiness({
        businessId,
        name: formData.name,
        description: formData.description,
        categoryId: formData.categoryId as any,
        subcategoryIds: formData.subcategoryIds as any[],
        address: formData.address,
        city: formData.city,
        state: formData.state,
        zipCode: formData.zipCode,
        phone: formData.phone || undefined,
        email: formData.email || undefined,
        website: formData.website || undefined,
        services,
        paymentMethods: formData.paymentMethods,
        languagesSpoken: formData.languagesSpoken,
        hours: formData.hours,
        isMuslimOwned: formData.isMuslimOwned,
        isHalalCertified: formData.isHalalCertified,
        hasPrayerSpace: formData.hasPrayerSpace,
        hasParking: formData.hasParking,
        isAccessible: formData.isAccessible,
        yearsInBusiness: formData.yearsInBusiness,
        facebookUrl: formData.facebookUrl || undefined,
        instagramUrl: formData.instagramUrl || undefined,
        twitterUrl: formData.twitterUrl || undefined,
        linkedinUrl: formData.linkedinUrl || undefined,
        youtubeVideoUrl: formData.youtubeVideoUrl || undefined,
      });

      toast.success("Business updated successfully!");
      onSuccess();
    } catch (error) {
      console.error("Error updating business:", error);
      toast.error("Failed to update business. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!business || !categories) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Edit Business Information</h2>
        <p className="text-gray-600">Update your business details and settings.</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Basic Information */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Business Name *
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent ${
                  formErrors.name ? "border-red-500" : "border-gray-300"
                }`}
              />
              {formErrors.name && (
                <p className="mt-1 text-sm text-red-600">{formErrors.name}</p>
              )}
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description *
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                required
                rows={4}
                className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent ${
                  formErrors.description ? "border-red-500" : "border-gray-300"
                }`}
                placeholder="Describe your business, what makes it special, and what services you offer..."
              />
              {formErrors.description && (
                <p className="mt-1 text-sm text-red-600">{formErrors.description}</p>
              )}
            </div>
          </div>
        </div>

        {/* Business Hours */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <BusinessHoursManager
            hours={formData.hours}
            onHoursChange={(hours) => setFormData(prev => ({ ...prev, hours }))}
            showSpecialHours={true}
          />
        </div>

        {/* YouTube Video */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">YouTube Video</h3>
          <YouTubeInput
            value={formData.youtubeVideoUrl}
            onChange={(value) => setFormData(prev => ({ ...prev, youtubeVideoUrl: value }))}
            error={formErrors.youtubeVideoUrl}
            placeholder="https://www.youtube.com/watch?v=..."
          />
          <p className="mt-2 text-sm text-gray-500">
            Add a YouTube video to showcase your business (optional)
          </p>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4">
          <button
            type="button"
            onClick={onCancel}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? "Updating..." : "Update Business"}
          </button>
        </div>
      </form>
    </div>
  );
}
