import { Authenticated, Unauthenticated, useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { SignInForm } from "./SignInForm";
import { SignOutButton } from "./SignOutButton";
import { Toaster } from "sonner";
import { HomePage } from "./components/HomePage";
import { BusinessDirectory } from "./components/BusinessDirectory";
import { BusinessDetail } from "./components/BusinessDetail";
import { AddBusiness } from "./components/AddBusiness";
import { BusinessDashboard } from "./components/BusinessDashboard";
import { UserProfile } from "./components/UserProfile";
import { useState } from "react";

export default function App() {
  const [currentPage, setCurrentPage] = useState<string>("home");
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [selectedBusiness, setSelectedBusiness] = useState<string>("");

  const loggedInUser = useQuery(api.auth.loggedInUser);

  const renderPage = () => {
    switch (currentPage) {
      case "directory":
        return (
          <BusinessDirectory
            categorySlug={selectedCategory}
            onBusinessSelect={(slug) => {
              setSelectedBusiness(slug);
              setCurrentPage("business");
            }}
            onBack={() => setCurrentPage("home")}
            onCategorySelect={(slug) => {
              setSelectedCategory(slug);
              setCurrentPage("directory");
            }}
          />
        );
      case "business":
        return (
          <BusinessDetail
            businessSlug={selectedBusiness}
            onBack={() => setCurrentPage("directory")}
          />
        );
      case "add-business":
        return (
          <AddBusiness
            onBack={() => setCurrentPage("home")}
            onSuccess={() => setCurrentPage("home")}
          />
        );
      case "dashboard":
        return (
          <BusinessDashboard
            onBack={() => setCurrentPage("add-business")}
          />
        );
      case "profile":
        return (
          <UserProfile
            onBack={() => setCurrentPage("home")}
          />
        );
      default:
        return (
          <HomePage
            onCategorySelect={(slug) => {
              setSelectedCategory(slug);
              setCurrentPage("directory");
            }}
            onAddBusiness={() => setCurrentPage("add-business")}
          />
        );
    }
  };

  if (loggedInUser === undefined) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <button
                onClick={() => setCurrentPage("home")}
                className="text-2xl font-bold text-green-600 hover:text-green-700"
              >
                🕌 Muslim Business Directory
              </button>
            </div>
            <div className="flex items-center space-x-2 sm:space-x-4">
              <Authenticated>
                <button
                  onClick={() => setCurrentPage("dashboard")}
                  className="hidden sm:block text-green-600 hover:text-green-700 font-medium"
                >
                  My Businesses
                </button>
                <button
                  onClick={() => setCurrentPage("dashboard")}
                  className="sm:hidden text-green-600 hover:text-green-700 font-medium text-sm"
                >
                  Dashboard
                </button>
                <button
                  onClick={() => setCurrentPage("add-business")}
                  className="bg-green-600 text-white px-3 sm:px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm sm:text-base"
                >
                  <span className="hidden sm:inline">Add Business</span>
                  <span className="sm:hidden">Add</span>
                </button>
                <button
                  onClick={() => setCurrentPage("profile")}
                  className="text-gray-600 hover:text-gray-700 text-sm sm:text-base"
                >
                  <span className="hidden sm:inline">Welcome, {loggedInUser?.email?.split("@")[0]}</span>
                  <span className="sm:hidden">Profile</span>
                </button>
              </Authenticated>
              <SignOutButton />
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Unauthenticated>
          <div className="max-w-md mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                Welcome to Muslim Business Directory
              </h1>
              <p className="text-gray-600">
                Discover and support Muslim-owned businesses in your community
              </p>
            </div>
            <SignInForm />
          </div>
        </Unauthenticated>

        <Authenticated>
          {renderPage()}
        </Authenticated>
      </main>

      <Toaster />
    </div>
  );
}
